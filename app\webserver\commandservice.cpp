﻿#include "commandservice.h"

#include <QCoreApplication>
#include <QElapsedTimer>

#include <unistd.h>
#include <websocket.h>
#include <QThread>
#include <QDateTime>
#include <QTime>
#include "udiskinfo.h"
#include "mechmonitor.h"
#include "networkdevicemanager.h"
#include "monitorservice.h"
#include "moisturecontroller.h"
#include "alarmfacade.h"
#include "statemonitor.h"
#include "usermanager.h"
#include "outwardservice.h"
#include "faultcode/faultcodedefine.h"
#include "auditmanager.h"
#include "log.h"
#include "dbconfig.h"
#include "modbusslaver.h"
#include "updatefirmware/firmwareupdatermanager.h"
#include "apprequestRouter.h"
#include "webrequesthandle/webrouter.h"
#include "webrequesthandle/iwebrequesthandler.h"
#include "chart/trend/temptrendchart.h"
#include "chart/trend/humitrendchart.h"

#ifdef Q_OS_WIN
#include <windows.h>
#endif
#ifdef Q_WS_QWS
#include "systemsettings.h"
#endif
namespace DBConfig = storage;
using namespace DBConfig;

QString WEB_VIEW_VERSION;
HttpSessionStore *CommandService::sessionStore= NULL;
StaticFileController *CommandService::staticFileController=NULL;
TemplateCache *CommandService::templateCache=NULL;
HttpSessionStore *CommandServiceForApp::sessionStorePM= NULL;
StaticFileController *CommandServiceForApp::staticFileControllerPM=NULL;
TemplateCache *CommandServiceForApp::templateCachePM=NULL;
bool g_bTest = false;

#define USE_PERMISSION(permission)\
{\
    if(!isIllegalUser(request, response))\
{\
    QJsonArray jsonUserInfo;\
    responseWrite(QJsonDocument(encapsulationData(jsonUserInfo, ERROR_COLDE_ILLEGAL_USER)).toJson());\
    return;\
    }\
    QString userName = getUserName(request);\
    if(!safe::UserManager::instance().isPermission(userName, permission))\
{\
    responseWrite(QJsonDocument(encapsulationData(ERROR_COLDE_NO_PERMISSION)).toJson());\
    return;\
    }\
}


/************************************************
 * 函数名:  responseWrite
 * 输入参数:  request -- http请求
 *      response -- 响应
 *      data -- 要写入的数据
 *      bLastPart -- 是否最后一份数据
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  响应服务"获取实时数据"
 ************************************************/
#define responseWrite(data) response.write(request.getParameter("jsonPCallback")+ data.constData());

/************************************************
 * 函数名:  CommandService
 * 输入参数:  configFile -- 配置文件名
 *      parent -- 父Object
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  构造函数
 ************************************************/
CommandService::CommandService(const QString &configFile, QObject *parent)
    : HttpRequestHandler(parent),
    m_pWebRouter(new WebRouter)
{
    // Session store
    if(NULL == sessionStore)
    {
        QSettings *sessionSettings = new QSettings(configFile, QSettings::IniFormat, this);
        sessionSettings->beginGroup("sessions");
        sessionStore = new HttpSessionStore(sessionSettings, this);
    }

    // Static file controller
    if(NULL == staticFileController)
    {
        QSettings *fileSettings = new QSettings(configFile, QSettings::IniFormat, this);
        fileSettings->beginGroup("files");
        staticFileController = new StaticFileController(fileSettings, this);
    }

    // Configure template cache
    if(NULL == templateCache)
    {
        QSettings *templateSettings = new QSettings(configFile, QSettings::IniFormat, this);
        templateSettings->beginGroup("templates");
        templateCache = new TemplateCache(templateSettings, this);
    }
    qRegisterMetaType< Modbus485::SerialInfo >("Modbus485::SerialInfo");
    qRegisterMetaType<ModbusSetting>("ModbusSetting");
}

CommandService::~CommandService()
{
    if(m_pWebRouter)
    {
        delete m_pWebRouter;
    }

    if(sessionStore)
    {
        delete sessionStore;
    }
    if(staticFileController)
    {
        delete staticFileController;
    }
    if(templateCache)
    {
        delete templateCache;
    }
}

/************************************************
 * 函数名:  service
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  服务函数
 ************************************************/
void CommandService::service(HttpRequest &request, HttpResponse &response)
{
    response.setHeader("Content-Type", "text/json");
    QByteArray path = request.getPath();

    bool bStaticRequest = false;
    bool bOperation = true;

    //新增请求路由，兼容以前的请求处理方式
    if(m_pWebRouter && m_pWebRouter->handleRequest(path, request, response))
    {
        return;
    }

    if (path == "/Push")//开启推送服务
    {
        bOperation = false;
        Push(request, response);
    }
    else if (path == "/Loginfo")//添加日志
    {
        WriteLogInfo(request, response);
    }
    else if (path == "/GetSysInfo")//获取系统设置
    {
        getSystemSetting(request, response);
    }
    else if (path == "/getMainStationParameter")//获取主站列表
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                getMainStationParameter( request, response );
    }
    else if(path == "/GetGroupNoRange") //获取主机区域对应的组号范围
    {
        getGroupNoRange(request, response);
    }
    else if(path == "/getFindGroupNoAduType") //组号扫描-获取支持的传感器类型
    {
        getFindGroupNoAduType(request, response);
    }
    else if(path == "/FindAduGroupNo") //开始扫描传感器组号接口
    {
        findAduGroupNo(request, response);
    }
    else if(path == "/CancelFindAduGroupNo") //取消传感器组号扫描
    {
        cancelFindAduGroupNo(request, response);
    }
    else if(path == "/GetFindAduGroupNoStatus") //获取扫描组号状态信息
    {
        getFindAduGroupNoStatus(request, response);
    }
    else if(path == "/PushFindAduGroupNoProgress") //推送最近一次组号扫描任务进度
    {
        pushFindAduGroupNoProgress(request, response);
    }

    else if(path == "/SetLocalTime")
    {
        USE_PERMISSION(safe::OPERATE_TIME_SET)
                setLocalTime(request, response);
    }
    else if (path == "/saveMainStationParameter")//保存主站参数
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                saveMainStationParameter( request, response );
    }
    else if (path == "/SaveSysInfo")//更新系统设置
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                saveSystemSetting(request, response);
    }
    else if (path == "/GetSysConfig")//获取系统设置参数
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                getSystemConfig(request, response);
    }
    else if (path == "/SaveNetWorkInfo")//更新网络设置
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                SaveNetWorkInfo(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_NET_SET, QDateTime::currentDateTime(), "");
    }
    else if(path == "/setSafeAlarmPara")  //设置告警参数
    {
        setSafeAlarmPara(request, response);
    }
    else if(path == "/getSafeAlarmPara")  //获取告警参数
    {
        getSafeAlarmPara(request, response);
    }
    else if(path == "/GetSafeAlarm")   //获取安全测评告警
    {
        GetSafeAlarm(request, response);
    }
    else if(path == "/SafeAlarmConfirm")  //告警确认
    {
        SafeAlarmConfirm(request, response);
    }
    else if (path == "/GetNetworkInfo")//获取网络设置
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                GetNetworkInfo(request, response);
    }
    else if (path == "/GetNetworkConfig")//获取网络设置参数
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                GetNetworkConfig(request, response);
    }
    else if (path == "/GetModbusInfo")//获取Modbus设置
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                GetModbusInfo(request, response);
    }
    else if (path == "/GetModbusConfig")//获取Modbus设置参数
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                GetModbusConfig(request, response);
    }
    else if (path == "/SaveModbus")//保存Modbus设置
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                SaveModbus(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_MODBUS_SET, QDateTime::currentDateTime());
    }
    else if (path == "/GetADUVersionList")//获取前端版本信息
    {
        GetADUVersionList(request, response);
    }
    else if (path == "/UpdateADU")//前端固件更新
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                UpdateADU(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FIRM_SET, QDateTime::currentDateTime());
    }
    else if (path == "/ExportData")//数据导出
    {

#ifdef USE_SAFE
        USE_PERMISSION(PDSSAFE_U::OPERATE_BACK_DATA)
        #endif
                ExportDBData(request, response);
    }
    else if (path == "/GetExportDataPath")//获取数据导出路径
    {
        GetExportDataPath(request, response);
    }
    else if (path == "/TodoPowerControl")//主机休眠响应
    {
        AttentionShutdown(request, response);
    }
    else if (path == "/GetStation")//获取站点信息
    {
        GetStation(request, response);
    }
    else if (path == "/SaveStationInfo")//保存站点信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                SaveStationInfo(request, response);
        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FILE_STATION_SET, QDateTime::currentDateTime());

    }
    else if (path == "/DeleteStation")//删除站点
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                DeleteStation(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FILE_STATION_SET, QDateTime::currentDateTime());
    }
    else if (path == "/GetDeviceNameList")//获取一次设备名称列表
    {
        GetDeviceNameList(request, response);
    }
    else if (path == "/GetDeviceInfo")//获取一次设备信息详情
    {
        GetDeviceInfo(request, response);
    }
    else if (path == "/SaveDeviceInfo")//保存/修改一次设备信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                SaveDeviceInfo(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FILE_DEVICE_SET, QDateTime::currentDateTime());
    }
    else if (path == "/DeleteDeviceInfo")//删除一次设备
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                DeleteDeviceInfo(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FILE_DEVICE_SET, QDateTime::currentDateTime());
    }
    else if(path == "/getCheckList") // 获取审核数据列表
    {
        getCheckList(request, response);
    }
    else if(path == "/checkManager") // 审核管理
    {
        USE_PERMISSION(safe::OPERATE_CHECK)
                checkManager(request, response);
    }
    else if(path == "/Login")  //登陆
    {
        QString strOpUserName = getUserName(request);
        if(!strOpUserName.isEmpty())
        {
            safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_LOGIN_IN, QDateTime::currentDateTime());
        }
        Login(request, response);
    }
    else if(path == "/Logout")
    {
        logout(request, response);
    }
    else if (path == "/GetDeviceList")//获取一次设备信息列表
    {
        GetDeviceList(request, response);
    }
    else if (path == "/GetPointNameList")//获取测点列表
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                GetPointNameList(request, response);
    }
    else if (path == "/SavePointInfo")//保存测点信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                SavePointInfo(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_SAVE_POINT_SET, QDateTime::currentDateTime());

    }
    else if (path == "/DeletePointInfo")//删除测点信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                DeletePointInfo(request, response);

        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_DEL_POINT_SET, QDateTime::currentDateTime());
    }
    else if (path == "/GetADUType")//获取所有前端类型
    {
        GetADUType(request, response);
    }
    else if(path == "/ChangeUserPas") // 更改用户密码
    {
        USE_PERMISSION(safe::OPERATE_CHANGE_PASS)
                ChangeUserPas(request, response);
    }
    else if (path == "/GetSyncDataADUType")//获取同步数据前端类型
    {
        GetSyncDataADUType(request, response);
    }
    else if (path == "/GetCommonType")//获取通用类型枚举接口
    {
        GetCommonType(request, response);
    }
    else if (path == "/GetADUList")//获取前端列表
    {

        GetADUList(request, response);
    }
    else if (path == "/GetADUInfo")//获取前端信息
    {
        GetADUInfo(request, response);
    }
    else if (path == "/SaveADUInfo")//保存前端信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                saveADUInfo(request, response);
    }
    else if (path == "/DeleteADUInfo")//删除前端信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                deleteADUInfo(request, response);

#ifdef USE_SAFE
        QString userName = getUserName(request);
        QString decUserName;
        PDSSAFE_U::UserManager::instance().rasDecrypt(userName, decUserName, true);
        AuditManager::instance().addAudit(decUserName, PDSSAFE_A::OPERATE_ADU_MANA, QDateTime::currentDateTime());
#endif
    }
    else if (path == "/GetChannelList")//获取前端下通道列表
    {
        getChannelList(request, response);
    }
    else if (path == "/GetChannelInfo")//获取通道信息
    {
        getChannelInfo(request, response);
    }

    else if (path == "/SaveChannelInfo")//保存通道信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                saveChannelInfo(request, response);
    }
    else if (path == "/GetRelationList")//获取关联列表
    {
        getRelationList(request, response);
    }
    else if (path == "/GetRelation")//获取关联信息
    {
        getRelation(request, response);
    }
    else if (path == "/SaveRelation")//保存关联信息
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                saveRelation(request, response);
    }
    else if (path == "/GetChartData")//获取图谱
    {
        USE_PERMISSION(safe::OPERATE_DATA_VIEW)
                getChartData(request, response);
    }
    else if (path == "/GetChartParamData")//获取图谱
    {
        USE_PERMISSION(safe::OPERATE_DATA_VIEW)
                getChartParamData(request, response);
    }
    else if (path == "/GetArresterChartParamData")//获取图谱
    {
        USE_PERMISSION(safe::OPERATE_DATA_VIEW)
                GetArresterChartParamData(request, response);
    }
    else if (path == "/GetTendData")//获取图谱
    {
        USE_PERMISSION(safe::OPERATE_DATA_VIEW)
                getTrendData(request, response);
    }
    else if (path == "/GetAduAlarmInfo")//获取前端告警数据
    {
        getAduAlarmInfo(request, response);
    }
    //----测试用----
    // 上报历史数据
    else if ( path == "/PDSTEST" )
    {
        pdsTestControl(request, response);
    }
    else if ( path == "/ReportHistoryData" )
    {
        reportHistoryData(request, response);
    }
    //----
    else if (path == "/ClearADUData")//擦除前端所有数据记录
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                clearADUData(request, response);
    }
    else if (path == "/WakeUpADU")//唤醒前端
    {
        USE_PERMISSION(safe::OPERATE_DATA_SYNC)
                wakeUpADU(request, response);
    }
    else if (path == "/SyncAduData")//同步数据
    {

        USE_PERMISSION(safe::OPERATE_DATA_SYNC)
                syncAduData(request, response);

#ifdef USE_SAFE
        QString userName = getUserName(request);
        QString decUserName;
        PDSSAFE_U::UserManager::instance().rasDecrypt(userName, decUserName, true);
        AuditManager::instance().addAudit(decUserName, PDSSAFE_A::OPERATE_DATA_SYNC, QDateTime::currentDateTime());
#endif
    }
    else if (path == "/GetADUId")//获取对应类型前端的id
    {
        GetADUId(request, response);
    }
    else if (path == "/ApplySensorConfig")//将该传感器参数应用于同类传感器
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                ApplySensorConfig(request, response);
    }
    else if (path == "/SwitchUser")//切换用户
    {
        SwitchUser(request, response);

#ifdef USE_SAFE
        QString userName = getUserName(request);
        QString decUserName;
        PDSSAFE_U::UserManager::instance().rasDecrypt(userName, decUserName, true);
        AuditManager::instance().addAudit(decUserName, PDSSAFE_A::OPERATE_LOGIN_IN, QDateTime::currentDateTime());
#endif
    }
    else if (path == "/GetLastDataID")//获取最新记录id
    {
        USE_PERMISSION(safe::OPERATE_DATA_VIEW)
                GetLastDataID(request, response);
    }
    else if (path == "/GetMonitorTableInfo")//获取监测表信息
    {
        bOperation = false;
        GetMonitorTableInfo(request, response);
    }
    else if (path == "/GetADUStateInfo")//获取前端状态
    {
        bOperation = false;
        GetADUStateInfo(request, response);
    }
    else if (path == "/GetADUStateColorThred")//获取前端状态
    {
        GetADUStateColorThred(request, response);
    }
    else if (path == "/GetADUConfig")//获取前端配置
    {
        getADUConfig(request, response);
    }
    else if (path == "/ApplyADUConfig")//应用到同种类型前端
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                ApplyADUConfig(request, response);

#ifdef USE_SAFE
        QString userName = getUserName(request);
        QString decUserName;
        PDSSAFE_U::UserManager::instance().rasDecrypt(userName, decUserName, true);
        AuditManager::instance().addAudit(decUserName, PDSSAFE_A::OPERATE_ADU_MANA, QDateTime::currentDateTime());
#endif
    }
    else if (path == "/GetAlarmConfigInfo")//获取报警信息（密度微水）
    {
        getAlarmConfigInfo(request, response);
    }
    else if (path == "/GetAlarmConfigInfoParam")//获取报警设置参数（密度微水）
    {
        getAlarmConfigInfoPara(request, response);
    }
    else if(path == "/SaveAlarmConfig")   // 保存报警配置（密度微水）
    {
        saveAlarmConfig(request, response);
    }
    else if(path == "/RemoveAlarmConfig")  // 删除报警配置（密度微水）
    {
        deleteAlarmConfig(request, response);
    }
    else if(path == "/GetAlarmConfigList")   // 获取报警配置表（密度微水）
    {
        getAlarmConfigTreeList(request, response);
    }
    else if(path == "/GetHistoryDatas")// 获取历史数据（密度微水）
    {
        getHistoryDatas(request, response);
    }
    else if(path == "/GetHistoryTreeList")// 获取历史数据树装结构（密度微水）
    {
        getHistoryTreeList(request, response);
    }
    else if(path == "/SetLogLevel")
    {
        setLogLevel(request, response);
    }
    else if(path == "/InitConfigFile")
    {
        initConfigFile(request, response);
    }
    else if(path == "/GetVersion")
    {
        getVersionInfo(request, response);
    }
    else if(path == "/ConnectionADU")
    {
        connectionADU(request, response);
    }
    else if(path == "/SampleADU")
    {

        USE_PERMISSION(safe::OPERATE_DATA_COLLECT)
                sampleADU(request, response);

#ifdef USE_SAFE
        QString userName = getUserName(request);
        QString decUserName;
        PDSSAFE_U::UserManager::instance().rasDecrypt(userName, decUserName, true);
        AuditManager::instance().addAudit(decUserName, PDSSAFE_A::OPERATE_DATA_COLLECT, QDateTime::currentDateTime());
#endif
    }
    else if(path == "/DisconnectionADU")
    {
        disconnectionADU(request, response);
    }
    else if(path == "/getADULogData") //获取传感器日志数据
    {
        getAduLogInfo(request, response);
    }
    else if(path == "/GetAuditData")
    {
        USE_PERMISSION(safe::OPERATE_AUDIT_DATA)
                getAuditData(request, response);
    }
    else if(path == "/forceChangeAduModel") //强制切换前端工作模式(无需和前端进行通信)
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                forceChnageAduModel(request, response);
    }
    else if(path == "/ClearAllData")   //清空数据
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                QString strOpUserName = getUserName(request);
        safe::CheckData ckData;
        ckData.checkType = safe::CHECK_CLEAREATA;
        ckData.sponsor = strOpUserName;
        ckData.dateTime = QDateTime::currentDateTime();
        safe::UserManager::instance().addCkData(ckData);

        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_CLEAR_DATA, QDateTime::currentDateTime());

        responseWrite(QJsonDocument(encapsulationData()).toJson());
    }
    else if(path == "/changeLanguage")
    {
        QString strOpUserName = getUserName(request);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_LANGUAGE_SET, QDateTime::currentDateTime(), "");
        changeLanguage(request, response);
    }
    else if(path == "/addNormalUser")
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                addNormalUser(request, response);
    }
    else if(path == "/deleteNormalUser")
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                deleteNormalUser(request, response);
    }
    else if(path == "/getNormalUserList")
    {
        USE_PERMISSION(safe::OPERATE_USERLIST)
                getNormalUserList(request, response);
    }
    else if(path == "/FactorySettings") //恢复出场设置
    {
        USE_PERMISSION(safe::OPERATE_SYS_SET)
                QString strOpUserName = getUserName(request);
        safe::CheckData ckData;
        ckData.checkType = safe::CHECK_FACTORYSET;
        ckData.sponsor = strOpUserName;
        ckData.dateTime = QDateTime::currentDateTime();
        safe::UserManager::instance().addCkData(ckData);
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FACTORYSET, QDateTime::currentDateTime(), "");
        responseWrite(QJsonDocument(encapsulationData()).toJson());
    }
    else if(path.endsWith(".tar.gz"))
    {
        staticFileController->sericeDownLoad( request, response);
    }
    else if(path.contains("videoPic/"))
    {
        bOperation = false;
        bStaticRequest = true;
#ifdef Q_WS_QWS
        path.remove(0, 10);
        PDS_SYS_INFO_LOG("path = %s", path.data());
#else
        path.remove(0, 9);
        path.insert(0, '.');
#endif

        QFile file(path);
        if (file.open(QIODevice::ReadOnly))
        {
            PDS_SYS_INFO_LOG("open success");
            response.setHeader("Content-Type", "image/jpeg");
            response.setHeader("Cache-Control","max-age="+ QByteArray::number(100000/100));
            if (file.size()<= 655360)
            {
                QByteArray tBuffer;
                while (!file.atEnd() && !file.error())
                {
                    tBuffer.append(file.read(65536));
                }
                response.write(tBuffer);
            }
            else
            {
                PDS_SYS_INFO_LOG("too big");
            }
            file.close();
        }
    }
    else
    {
        //LogCheck 基本为获取静态界面，不算异常情形
        //logWarnning( QString("Unable to deal with request : %1").arg(QString::fromLatin1(path)) );
        bOperation = false;
        bStaticRequest = true;
        staticFileController->service(request,response);
    }

    //    if ( bOperation )
    //    {
    //        //do something
    //        MonitorService::instance().webOperateSyncTimer();
    //    }

    //    if( !bStaticRequest )
    //    {
    //        //LogCheck 完善打印信息
    //        logInfo( QString("Finish to deal with request, client ip : %1, request path : %2, http Parameter : ")
    //                  .arg(request.getPeerAddress().toString()).arg(QString::fromLatin1(path)) )<< request.getParameterMap();
    //    }
#ifdef USE_WEBSERVER_LOG
    m_spLogger->clear(true, true);
#endif
}

/************************************************
 * 函数名:  saveSystemSetting
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存系统设置信息
 ************************************************/
void CommandService::saveSystemSetting(HttpRequest &request, HttpResponse &response)
{

    Q_UNUSED(response);
    ConfigService &configs = ConfigService::instance();
    SystemSetting systemSetting = configs.systemSettings();

    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_SYS_SET, QDateTime::currentDateTime());
    QDateTime dateTime = QDateTime::fromString(request.getParameter(STR_SYS_TIME), STR_DATE_TIME_FORMAT);
    if (dateTime.isValid())
    {
        PDS_SYS_INFO_LOG("Set local time: %s", dateTime.toString().toLatin1().data());
        systemSetting.stsystemTime = dateTime;
#ifdef Q_WS_QWS
        set_system_date((signed char*)(systemSetting.stsystemTime.toString(STR_DATE_TIME_QSTRING_ARM).toLatin1().data()));
#endif
#ifdef Q_OS_WIN
        SYSTEMTIME st;
        st.wYear = systemSetting.stsystemTime .date().year();
        st.wMonth = systemSetting.stsystemTime .date().month();
        st.wDay = systemSetting.stsystemTime .date().day();
        st.wHour = systemSetting.stsystemTime .time().hour();
        st.wMinute = systemSetting.stsystemTime .time().minute();
        st.wSecond = systemSetting.stsystemTime .time().second();
        SetLocalTime(&st);
#endif
    }
    else
    {
        PDS_SYS_INFO_LOG("Begin to set system setting");
        systemSetting.iUploadInterval = request.getParameter(STR_SYS_UPLOAD_INTERVAL).toInt();
        systemSetting.iFreq = request.getParameter(STR_FREQUNCY).remove(2,2).toInt();
        //传感器连接断开状态判断阈值
        systemSetting.uiAduOfflineThreshold = request.getParameter("aduOfflineThreshold").toUInt();
        //辅控RtuID、汇集RtuID配置
        ConfigService::instance().setAuxRtuID(request.getParameter("auxRtuID"));
        ConfigService::instance().setIedRtuID(request.getParameter("iedRtuID"));

        if(MONITOR_TYPE_LOW_POWER == configs.getHardType())
        {
            systemSetting.uiMonitorSleepSpace = request.getParameter(STR_MONITOR_SLEEP_SPACE).toInt();
            systemSetting.uiMonitorAwakeStartTime = request.getParameter(STR_WAKE_START_TIME).toInt();
            emit sigMonitorShutDownInfo();
        }
#ifdef SERVER_IS_OLD_PROTOCOL
        systemSetting.uiSampleinterval = request.getParameter(STR_SAMPLE_INTERVAL).toInt();
        SmartSensor::instance().setHisInterval(systemSetting.uiSampleinterval);

        if(MONITOR_TYPE_LOW_POWER == configs.getHardType())
        {
            systemSetting.iMonitorSampleStartTime = request.getParameter(STR_MONITOR_SAMPLE_TIME).toInt();
            systemSetting.iMonitorSampleSpace = request.getParameter(STR_MONITOR_SAMPLE_INTERVAL).toInt();
        }
#endif
        systemSetting.uiPdSampleInterval = request.getParameter(STR_MONITOR_PD_SAMPLE_TIME).toInt();
        MonitorService::instance().setSampInterval(systemSetting.uiPdSampleInterval, ADU_TYPE_UNKNOW);

#ifdef SERVER_MECH
        systemSetting.uiMechSyncDataInterval = request.getParameter(STR_MONITOR_MECH_SAMPLE_TIME).toInt();
        MechMonitor::instance().setSampleInterval(systemSetting.uiMechSyncDataInterval);
#endif

        // 前端传回的param中可能不存在languange字段，需要进行过滤
        if (request.getParameterMap().contains(STR_LANGUAGE))
        {
            systemSetting.strLanguage = request.getParameter(STR_LANGUAGE);
        }

#ifdef SERVER_LORA_BEAN
        bool ucMonitorConnectionGroupflag = false; //组号修改标志
        quint16 ucMonitorConnectionGroup = 0;
        if (request.getParameter(STR_MONITOR_WORK_GROUP).toInt() != systemSetting.ucMonitorConnectionGroup)
        {
            ucMonitorConnectionGroup = request.getParameter(STR_MONITOR_WORK_GROUP).toInt();
            ucMonitorConnectionGroupflag = true;

            systemSetting.ucMonitorConnectionGroup = ucMonitorConnectionGroup;
            MonitorService::instance().changeGroup(ucMonitorConnectionGroup);
        }

        /*bool uiloraFreflag = false; //lora频点修改标志
        quint16 uiloraFre = 0;
        if (request.getParameterMap().contains(STR_MONITOR_LORA_FREQUENCEY))
        {
            if(request.getParameter(STR_MONITOR_LORA_FREQUENCEY).toInt() != (quint16)systemSetting.loraFreConf)
            {
                uiloraFre = request.getParameter(STR_MONITOR_LORA_FREQUENCEY).toInt();
                uiloraFreflag = true;
            }
        }

        if(uiloraFreflag && !ucMonitorConnectionGroupflag)
        {
            if(systemSetting.ucMonitorConnectionGroup <= LoraInfoKeys[uiloraFre].uiGroupIdLimit)
            {
                systemSetting.loraFreConf = (LoraFrequencyArea)uiloraFre;
                NetworkDeviceManager::instance().getLoraDevice()->setLoraTransPower(LoraInfoKeys[systemSetting.loraFreConf].eTransPwr);
                NetworkDeviceManager::instance().getLoraDevice()->setLoraBandType(LoraInfoKeys[systemSetting.loraFreConf].eBand);
            }
        }
        else if(!uiloraFreflag && ucMonitorConnectionGroupflag)
        {
            if(ucMonitorConnectionGroup <= LoraInfoKeys[systemSetting.loraFreConf].uiGroupIdLimit)
            {
                systemSetting.ucMonitorConnectionGroup = ucMonitorConnectionGroup;
                MonitorService::instance().changeGroup(ucMonitorConnectionGroup);
            }
        }
        else if(uiloraFreflag && ucMonitorConnectionGroupflag)
        {
            if(ucMonitorConnectionGroup <= LoraInfoKeys[uiloraFre].uiGroupIdLimit)
            {
                systemSetting.ucMonitorConnectionGroup = ucMonitorConnectionGroup;
                MonitorService::instance().changeGroup(ucMonitorConnectionGroup);

                systemSetting.loraFreConf = (LoraFrequencyArea)uiloraFre;
                NetworkDeviceManager::instance().getLoraDevice()->setLoraTransPower(LoraInfoKeys[systemSetting.loraFreConf].eTransPwr);
                NetworkDeviceManager::instance().getLoraDevice()->setLoraBandType(LoraInfoKeys[systemSetting.loraFreConf].eBand);
            }
        }*/
#endif
        systemSetting.strMonitorName = request.getParameter("MonitorName");
        QString strMonitorType = request.getParameter("MonitorType");
        systemSetting.eMonitorHostType = (MonitorHostType)ConfigService::instance().listMonitorType().key(strMonitorType, MONITOR_TYPE_UNKNOW);
        if(MONITOR_TYPE_UNKNOW == systemSetting.eMonitorHostType)
        {
            PDS_SYS_ERR_LOG("unknow monitor type, str = %s", strMonitorType.toLatin1().data());
            return;
        }
        configs.savesystemSetting(systemSetting);
        if(MONITOR_TYPE_LOW_POWER == ConfigService::instance().getHardType())
        {
            SmartSensor::instance().computeTimer();
        }

    }
    //qDebug()<<"********************+++++++"<<NetworkDeviceManager::instance().getLoraDevice()->getLoraTransPower();

    QString strOpUserName = getUserName(request);
    safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_SYS_SET, QDateTime::currentDateTime(), "");
    responseWrite(QJsonDocument(encapsulationData()).toJson());
    return;
}

/************************************************
 * 函数名:  getSystemSetting
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取系统设置信息
 ************************************************/
void CommandService::getSystemSetting(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)
    ConfigData data;
    data.SystemSettingData();
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

void CommandService::getFindGroupNoAduType(HttpRequest &request, HttpResponse &response)
{
    Others otherData;
    otherData.getFindGroupNoAduType();
    responseWrite(QJsonDocument(encapsulationData(otherData)).toJson());
}

void CommandService::getGroupNoRange(HttpRequest &request, HttpResponse &response)
{
    const int iLorafrequency = request.getParameter(STR_MONITOR_LORA_FREQUENCEY).toInt();
    if(iLorafrequency < 0 || iLorafrequency > (int)AREA_CHINA_RSV)
    {
        HTTPErrorCode eErrorCode = ERROR_CODE_PARAMETER_ERROR;
        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
        return;
    }
    ConfigData data;
    data.getGroupNoRange(iLorafrequency);
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

void CommandService::findAduGroupNo(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    const ADUType eAduType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    const QStringList strFindAduList = QString(request.getParameter(STR_ADU_LIST)).split(",", QString::SkipEmptyParts);
    const int iStartGroupNo = QString(request.getParameter("startGroupNo")).toInt();
    const int iEndGroupNo = QString(request.getParameter("endGroupNo")).toInt();

    if(ADU_TYPE_UNKNOW == eAduType || strFindAduList.isEmpty() || iEndGroupNo < 0 || iStartGroupNo < 0 || iEndGroupNo < iStartGroupNo)
    {
        eErrorCode = ERROR_CODE_PARAMETER_ERROR;
    }
    else
    {
        logInfo("CommandService::findAduGroupNo") << eAduType << strFindAduList << iStartGroupNo << iEndGroupNo;

        MonitorService::instance().findAduGroupNo(eAduType, strFindAduList, iStartGroupNo, iEndGroupNo);
    }

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

void CommandService::cancelFindAduGroupNo(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    const ADUType eAduType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

    logInfo("CommandService::cancelFindAduGroupNo") << eAduType;


    MonitorService::instance().cancelFindAduGroupNo(eAduType);

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

void CommandService::getFindAduGroupNoStatus(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(request)
    logInfo("CommandService::getFindAduGroupNoStatus");
    QJsonObject result;

    MonitorService::instance().getFindAduGroupNoStatus(result);

    responseWrite(QJsonDocument(encapsulationData(result)).toJson());
}

void CommandService::pushFindAduGroupNoProgress(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(request)
    logInfo("CommandService::pushFindAduGroupNoStatus");
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    MonitorService::instance().pushFindAduGroupNoProgress();

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

/************************************************
 * 函数名:  Push
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  开启推送服务
 ************************************************/
void CommandService::Push(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    QJsonObject data;
    data.insert(STR_PORT,quiPort);
    data.insert(STR_SOCKET_NAME,strSocketName);
#ifdef USE_SAFE
    data.insert(STR_NEED_LOGINSCENE, 1);  //是否需要登录
#else
    data.insert(STR_NEED_LOGINSCENE, 0);  //是否需要登录
#endif
    //data.insert("releaseEdition", "PdSafe");  //web版本
    data.insert("releaseEdition", ConfigService::instance().getSystemReleaseInfo().strReleaseEdition);
    data.insert("language", ConfigService::instance().getSystemReleaseInfo().strlanguage);

    MonitorHostType hardType = ConfigService::instance().getHardType();
    if(MONITOR_TYPE_HANGING == hardType)
    {
        data.insert("MonitorType", "MonitorTypeHanging");
    }
    else if(MONITOR_TYPE_COLLOCTION_NODE == hardType)
    {
        data.insert("MonitorType", "MonitorTypeCollectionNode");
    }
    else if(MONITOR_TYPE_AUX == hardType)
    {
        data.insert("MonitorType", "MonitorTypeAux");
    }
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}


/************************************************
 * 函数名:  GetDevicePointList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 获取设备列表树
 ************************************************/
void CommandService::GetDevicePointList(HttpRequest &request, HttpResponse &response)
{
    ConfigData data;
    data.DeviceTreeData(request.getParameter(STR_DEVICE_ID));
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  GetStation
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取站点信息
 ************************************************/
void CommandService::GetStation(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.getStation();

    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

/************************************************
 * 函数名:  SaveStationInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存站点信息
 ************************************************/
void CommandService::SaveStationInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();

    StationNode strStationNode;
    strStationNode.strName = request.getParameter(STR_STATION_NAME);
    QList<QString> voltage = ConfigService::instance().listVoltageLevelName();
    strStationNode.eVoltage = (VoltageLevel)voltage.indexOf(request.getParameter(STR_STATION_VOLTAGE));
    strStationNode.strCompany = request.getParameter(STR_STATION_COMPANY);
    strStationNode.strPMS = request.getParameter(STR_STATION_PMS);

    QString aduit = strStationNode.strName + "`" + strStationNode.eVoltage + "`" +  strStationNode.strCompany;
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_FILE_STATION_SET, QDateTime::currentDateTime(), aduit);
    configs.saveStationInfo(strStationNode);

    //发送信号至数据库
    QString newGUID = ConfigService::instance().stationNode().strSiteGUID;
    PDS_SYS_INFO_LOG("SaveStationInfo %s", newGUID.toLatin1().data());
    emit  sigStationMD5(newGUID);

    responseWrite(QJsonDocument(encapsulationData()).toJson());

}

/************************************************
 * 函数名:  GetDeviceList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取设备信息列表
 ************************************************/
void CommandService::GetDeviceList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.getDeviceList();
    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

/************************************************
 * 函数名:  GetDeviceNameList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取设备名称列表
 ************************************************/
void CommandService::GetDeviceNameList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.getDeviceNameList();
    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

/************************************************
 * 函数名:  GetDeviceInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取设备详细信息
 ************************************************/
void CommandService::GetDeviceInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.GetDeviceInfo(request.getParameter(STR_DEVICE_ID));
    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

/************************************************
 * 函数名:  SaveDeviceInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存设备详细信息
 ************************************************/
void CommandService::SaveDeviceInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();
    DeviceNode device;
    device.strName = request.getParameter(STR_DEVICE_NAME);
    device.strDeviceGUID = request.getParameter(STR_DEVICE_ID);
    device.strPMS = request.getParameter(STR_DEVICE_PMS);

    QString aduit = device.strName + "`" + device.strDeviceGUID + "`" +   device.strPMS;
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_FILE_DEVICE_SET, QDateTime::currentDateTime(), aduit);

    QList<QString> strDevice = ConfigService::instance().listDeviceTypeName();
    device.eDeviceType = (DeviceType)(strDevice.indexOf(request.getParameter(STR_DEVICE_TYPE_NAME)) + 1);
    QList<QString> voltage = ConfigService::instance().listVoltageLevelName();
    device.eVoltage = (VoltageLevel)voltage.indexOf(request.getParameter(STR_DEVICE_VOL_LEVEL));

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    if (device.strDeviceGUID.isEmpty())
    {
        device.strDeviceGUID = configs.getGUID();
        if(CONFIG_DEVICE_EXISTED == configs.addDevice( device ))
        {
            responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_DEVICE_EXISTED)).toJson());
            return;
        }
        PDS_SYS_INFO_LOG("Begin to add device %s", device.strDeviceGUID.toLatin1().data());
    }
    else
    {
        eErrorCode = (HTTPErrorCode)configs.updateDevice( device );
        PDS_SYS_INFO_LOG("Begin to update device :%s", device.strDeviceGUID.toLatin1().data());
    }
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

/************************************************
 * 函数名:  DeleteDeviceInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除指定设备
 ************************************************/
void CommandService::DeleteDeviceInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    ConfigService &configs = ConfigService::instance();
    eErrorCode = (HTTPErrorCode)configs.delDevice(request.getParameter(STR_DEVICE_ID));

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}

/************************************************
 * 函数名:  GetPointNameList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取指定设备下的测点列表
 ************************************************/
void CommandService::GetPointNameList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others OthersData;
    QString strDeviceID = request.getParameter(STR_DEVICE_ID);

    OthersData.GetPointNameList(strDeviceID);

    responseWrite(QJsonDocument(encapsulationData(OthersData)).toJson());
}

/************************************************
 * 函数名:  SavePointInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存测点信息
 ************************************************/
void CommandService::SavePointInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();
    TestPointInfo stPoint;
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    stPoint.strName = request.getParameter(STR_POINT_NAME);
    stPoint.strPointGUID = request.getParameter(STR_POINT_ID);
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_SAVE_POINT_SET, QDateTime::currentDateTime(), stPoint.strName);

    QString strDeviceID = request.getParameter(STR_DEVICE_ID);

    if (stPoint.strPointGUID.isEmpty())
    {
        stPoint.strPointGUID = configs.getGUID();
        eErrorCode = (HTTPErrorCode)configs.addTestPoint(strDeviceID, stPoint);
        PDS_SYS_INFO_LOG("Begin to add point : %s", stPoint.strPointGUID.toLatin1().data());
    }
    else
    {
        eErrorCode = (HTTPErrorCode)configs.updateTestPoint(strDeviceID, stPoint);
        PDS_SYS_INFO_LOG("Begin to add point : %s", stPoint.strPointGUID.toLatin1().data());
    }
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}

/************************************************
 * 函数名:  DeletePointInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除测点信息
 ************************************************/
void CommandService::DeletePointInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_DEL_POINT_SET, QDateTime::currentDateTime(), request.getParameter(STR_POINT_ID));
    HTTPErrorCode eErrorCode = (HTTPErrorCode)configs.delTestPoint(request.getParameter(STR_POINT_ID));

    PDS_SYS_INFO_LOG("Delete point info, result code :%s", request.getParameter(STR_POINT_ID).data());
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}

/************************************************
 * 函数名:  GetADUId
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取某类型前端的id
 ************************************************/
void CommandService::GetADUId(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others OthersData;
    OthersData.getADUID(request.getParameter(STR_ADU_TYPE));
    responseWrite(QJsonDocument(encapsulationData(OthersData)).toJson());
}

/************************************************
 * 函数名:  ApplySensorConfig
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  将该传感器参数应用于同类传感器
 ************************************************/
void CommandService::ApplySensorConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ADUChannelInfo channel;
    channel.etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    PDS_SYS_INFO_LOG("Begin to set sensor config, channel type : %d, adu type : %d", channel.etype, eADUType);
    switch (channel.etype)
    {
    case CHANNEL_AE://AE参数
    {
        channel.stachpara.staAEPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staAEPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staAEPara.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staAEPara.usSampelCount = request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
        channel.stachpara.staAEPara.fTriggerThreshold = request.getParameter(STR_PARA_TRIGGER_THRESHOLD).toFloat();
        channel.stachpara.staAEPara.sOpenTime = request.getParameter(STR_PARA_OPEN_TIME).toInt();
        channel.stachpara.staAEPara.sCloseTime = request.getParameter(STR_PARA_CLOSE_TIME).toInt();
        channel.unID = 0;//TODO 协议中无该索引值，目前只能手动填充
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        channel.stachpara.staTEVPara.cBackGroundNum = request.getParameter(STR_PARA_BACK_GROUND_DATA).toInt();
        channel.unID = 1;//TODO 协议中无该索引值，目前只能手动填充
    }
        break;
    case CHANNEL_UHF:
    {
        channel.stachpara.staUHFPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staUHFPara.cBandWidth = ConfigService::instance().getBandWidthEnum(request.getParameter(STR_PARA_FILTER));
        channel.stachpara.staUHFPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staUHFPara.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staUHFPara.usSampelCount = 60;

    }
        break;
    case CHANNEL_HFCT:
    {
        channel.stachpara.staHFCTPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staHFCTPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staHFCTPara.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staHFCTPara.usSampelCount = request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
    }
        break;
    case CHANNEL_TEVPRPS:
    {
        channel.stachpara.staTEVPRPSPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staTEVPRPSPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staTEVPRPSPara.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staTEVPRPSPara.usSampelCount = request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
    }
        break;
    case CHANNEL_MECH:
    {
        channel.stachpara.staMechPara.usLoopCurrentThred = request.getParameter(STR_PARA_LOOP_CURRENT_THRED).toInt();
        channel.stachpara.staMechPara.usMotorCurrentThred = request.getParameter(STR_PARA_MOTOR_CURRENT_THRED).toInt();
        channel.stachpara.staMechPara.bSwitchState = request.getParameter(STR_PARA_SWITCH_STATE).toInt();
        channel.stachpara.staMechPara.bBreakerType = request.getParameter(STR_PARA_BREAK_TYPE).toInt();
        channel.stachpara.staMechPara.bMotorFunctionType = request.getParameter(STR_PARA_MOTOR_FUNCTION_TYPE).toInt();
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        channel.stachpara.staArresterIPara.eChannelPhase = (ChannelPhase)request.getParameter(STR_PARA_CHANNEL_PHASE).toInt();
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        channel.stachpara.staArresterUPara.eChannelPhase = (ChannelPhase)request.getParameter(STR_PARA_CHANNEL_PHASE).toInt();
        channel.stachpara.staArresterUPara.fTransformationRatio = request.getParameter(STR_PARA_TRANSFORMATION_RATIO).toFloat();
    }
        break;
    case CHANNEL_VIBRATION:
    {
        channel.stachpara.staVibrationParam.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staVibrationParam.usSampelCount = request.getParameter(STR_PARA_SAMPLE_RATE).toInt();
    }
        break;
    case CHANNEL_SAW:
    {

    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staTempParam.fUpperThreshold = request.getParameter("upThreshold").toFloat();
        channel.stachpara.staTempParam.fLowerThreshold = request.getParameter("lowerThreshold").toFloat();
        channel.stachpara.staTempParam.fChangedThreshold = request.getParameter("changeThreshold").toFloat();
        channel.unID = 0;

    }
        break;
    case CHANNEL_FLOOD:
    {
        channel.stachpara.stFloodChannelPara.cFlood = request.getParameter(STR_PARA_ALARM).toInt();
        channel.unID = 0;

    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staHumParam.fUpperThreshold = request.getParameter("upThreshold").toFloat();
        channel.stachpara.staHumParam.fLowerThreshold = request.getParameter("lowerThreshold").toFloat();
        channel.stachpara.staHumParam.fChangedThreshold = request.getParameter("changeThreshold").toFloat();
        channel.unID = 1;

    }
        break;
    case CHANNEL_SF6:
    {
        channel.stachpara.stSf6ChannelPara.fOverPressureThreshold = request.getParameter("OverPressureThreshold").toFloat();
        channel.stachpara.stSf6ChannelPara.fLowPressureThreshold = request.getParameter("LowPressureThreshold").toFloat();
        channel.stachpara.stSf6ChannelPara.fShutThreshold = request.getParameter("ShutThreshold").toFloat();
        channel.stachpara.stSf6ChannelPara.fVolumeRatio = request.getParameter("VolumeRatio").toFloat();
    }
        break;
    default:
    {
        PDS_SYS_WARNING_LOG("Unsupport channel type %d", channel.etype);
        break;
    }
    }
    if (eADUType == ADU_PD_THREE || eADUType == ADU_PD_FIVE)
    {
        QString strChannelName = QString(request.getParameter(STR_CHANNEL_TYPE));
        if(strChannelName == "TEV")
        {
            ADUChannelInfo channel;
            channel.etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
            channel.unID = request.getParameter(STR_CHANNEL_INDEX).toInt();
            channel.strName = request.getParameter(STR_CHANNEL_TYPE);
            channel.stachpara.staTEVPara.cBackGroundNum = request.getParameter(STR_PARA_BACK_GROUND_DATA).toInt();

            QStringList listADUID = ConfigService::instance().ADUID(eADUType);
            for (int i = 0; i < listADUID.size(); i++)
            {
                ConfigService::instance().saveChannelPara(channel.stachpara, listADUID.at(i), 1);
            }
        }
        if(strChannelName == "AE")
        {
            channel.stachpara.staAEPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
            QStringList listADUID = ConfigService::instance().ADUID(eADUType);
            for (int i = 0; i < listADUID.size(); i++)
            {
                MonitorService::instance().setChannelInfo(listADUID.at(i), eADUType, channel.unID, channel.stachpara);
                ConfigService::instance().saveChannelPara( channel.stachpara, listADUID.at(i), 0 );
            }
        }
    }
    else if (eADUType == ADU_TEMP_HUM_IS)
    {
        MonitorService::instance().setChannelInfo("", eADUType, channel.unID, channel.stachpara);
    }
    else
    {
        if( (eADUType == ADU_PD_IS) && (channel.etype == CHANNEL_TEV) )    //TEV通道需要特殊处理
        {
            //TODO TEV参数只需保存在主机上
            QList<ADUUnitInfo> aduPdis = ConfigService::instance().ADUList(eADUType);
            foreach(ADUUnitInfo aduInfo, aduPdis)
            {
                ConfigService::instance().saveChannelPara(channel.stachpara, aduInfo.strID, channel.unID);
            }
        }
        else
        {
            MonitorService::instance().setChannelInfo("", eADUType, channel.unID, channel.stachpara);
            //  ConfigService::instance().saveChannelName("", channel.unID, channel.strName);
        }
    }
    responseWrite(QJsonDocument(encapsulationData()).toJson());

}

/************************************************
 * 函数名:  GetADUTree
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端树信息
 ************************************************/
void CommandService::GetADUTree(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.GetADUTree();
    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

/************************************************
 * 函数名:  GetADUVersionList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端版本号信息
 ************************************************/
void CommandService::GetADUVersionList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    QJsonArray data = configData.GetADUVersionList(request.getParameter(STR_ADU_TYPE));
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  UpdateADU
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  前端固件更新
 ************************************************/
void CommandService::UpdateADU(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    HTTPErrorCode eErrorCode = ERROR_CODE_UPDATE_ADU_FILE_ERROR;

    if (WebSocket::instance()->isADUUpdateValid())
    {
        QTemporaryFile *uploadFile = request.getUploadedFile(STR_FILE_DATA);
        if ((uploadFile != NULL) && uploadFile->open())
        {
            QByteArray fileData = uploadFile->readAll();
            QString strADUUpdateFileName = request.getParameter(STR_FILE_DATA);
            ADUType eADUType = ConfigService::instance().getADUTypEnumUp(strADUUpdateFileName);

            if (isADUUpdateFileValid(eADUType, fileData))
            {
                QString aduList =  request.getParameter(STR_ADU_LIST);
                QStringList strListadu = aduList.split(",");
                ConfigService &configService = ConfigService::instance();

                if(configService.isADUUpdateADUIDValid(strListadu, eADUType))
                {
                    WebSocket::instance()->getADUUpdateInfo(strADUUpdateFileName, strListadu, fileData);
                    eErrorCode = ERROR_CODE_NONE_ERROR;
                }
                else
                {
                    eErrorCode = ERROR_CODE_UPDATE_ADU_ADU_ID_ERROR;
                    PDS_SYS_WARNING_LOG("ADU cann't update, adu ids %s", aduList.toLatin1().data());
                }
            }
            else
            {
                PDS_SYS_WARNING_LOG("Adu update file %s is invalid, crc check error", strADUUpdateFileName.toLatin1().data());
            }
        }
        else
        {
            PDS_SYS_WARNING_LOG("Fail to read update file!");
        }
    }
    else
    {
        eErrorCode = ERROR_CODE_UPDATE_ADU_UPDATING;
        PDS_SYS_WARNING_LOG("Host is busy, cann't update adu!");
    }

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}

/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能：设置本机时间
 ************************************************/
void CommandService::setLocalTime(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    ConfigService &configs = ConfigService::instance();
    SystemSetting systemSetting = configs.systemSettings();
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_TIME_SET, QDateTime::currentDateTime());
    QDateTime dateTime = QDateTime::fromString(request.getParameter(STR_SYS_TIME), STR_DATE_TIME_FORMAT);
    if (dateTime.isValid())
    {
        logTrace( QString("Set local time : %1").arg(dateTime.toString()) );
        systemSetting.stsystemTime = dateTime;
#ifdef Q_WS_QWS
        set_system_date((signed char*)(systemSetting.stsystemTime.toString(STR_DATE_TIME_QSTRING_ARM).toLatin1().data()));
        logInfo(QString("set datetime : ") + systemSetting.stsystemTime.toString(STR_DATE_TIME_QSTRING_ARM));
#endif
#ifdef Q_OS_WIN
        SYSTEMTIME st;
        st.wYear = systemSetting.stsystemTime .date().year();
        st.wMonth = systemSetting.stsystemTime .date().month();
        st.wDay = systemSetting.stsystemTime .date().day();
        st.wHour = systemSetting.stsystemTime .time().hour();
        st.wMinute = systemSetting.stsystemTime .time().minute();
        st.wSecond = systemSetting.stsystemTime .time().second();
        SetLocalTime(&st);
#endif
    }
    else
    {
        warningLog() << "time is unValid";
    }

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}



/************************************************
 * 函数名:  GetModbusInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取modbus信息
 ************************************************/
void CommandService::GetModbusInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.ModbusSettingData();
    ConfigData stdconfigData;
    stdconfigData.StdModbusSettingData();

    QJsonArray array;
    array.append(stdconfigData);
    array.append(configData);
    responseWrite(QJsonDocument(encapsulationData(array)).toJson());
}

/************************************************
 * 函数名:  GetModbusConfig
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取modbus信息参数
 ************************************************/
void CommandService::GetModbusConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.ModbusSettingConfig();
    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

void CommandService::getAlarmConfigInfoPara(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigData alarmConfigInfoPara;
    alarmConfigInfoPara.SF6AlarmConfigInfoPara();

    responseWrite(QJsonDocument(encapsulationData(alarmConfigInfoPara)).toJson());
}



/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 功能:  更改语言
 ************************************************/
void CommandService::changeLanguage(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strLan = request.getParameter("lan");
    ConfigService::instance().saveLanguage(strLan);
    responseWrite(QJsonDocument(encapsulationData()).toJson());
}


/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 功能:  强制切换前端工作模式
 ************************************************/
void CommandService::forceChnageAduModel(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QString strAduId = request.getParameter(STR_ADU_ID);
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    Monitor::ADUWorkMode mode = ConfigService::instance().getADUWorkModeEnum(request.getParameter("workMode"));
    int sleepTime =  request.getParameter(STR_ADU_SLEEP_TIME).toInt();
    ADUUnitInfo adu;
    if(ConfigService::instance().getADU(strAduId, eADUType, adu))
    {
        adu.stADUParam.eADUWorkModel = mode;
        if( adu.stADUParam.eADUWorkModel ==  Monitor::WORKER_MODEL_MAINTAIN)
        {
            if((sleepTime != 0) && (sleepTime != 1))
            {
                PDS_SYS_WARNING_LOG("WORKER_MODEL_MAINTAIN sleepTime = %d", sleepTime);
                sleepTime = 1;
            }
            adu.stADUParam.uiSleepTime =  sleepTime;
        }
        ConfigService::instance().saveADUInfo(strAduId, adu);
        responseWrite(QJsonDocument(encapsulationData()).toJson());
    }
    else
    {
        responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_CONFIG_ADU_ID_NOT_EXISTED)).toJson());
    }
}


/************************************************
         * 功能:  保存主站配置参数
         * 输入参数:  request -- http请求
         *      response -- 响应
         ************************************************/
void CommandService::saveMainStationParameter( HttpRequest &request, HttpResponse &response )
{
    QList<I2Set> hosts = ConfigService::instance().getI2Set();
    if(hosts.size() < 2)
    {
        PDS_SYS_ERR_LOG("saveMainStationParameter fail %d", hosts.size());
    }
    I2Set host;
    int nID = request.getParameter("id").toInt();
    host.strIP = request.getParameter("cagIp");
    host.nPort = request.getParameter("cagPort" ).toInt();
    host.strUrl = request.getParameter("cagUri" );
    host.strUser = request.getParameter("user" );
    host.strPasswd = request.getParameter( "password" );
    host.nHeartInterval = request.getParameter("dataAcquireTime").toInt();
    host.nDataInterval = request.getParameter("heartBeatTime").toInt();
    ConfigService::instance().setAuxRtuID(request.getParameter("auxRtuID"));
    ConfigService::instance().setIedRtuID(request.getParameter("iedRtuID"));
    if ( nID == 0 )
    {
        host.netStatus = hosts[0].netStatus;
        hosts[0] = host;
    }
    else if ( nID == 1 )
    {
        host.netStatus = hosts[1].netStatus;
        hosts[1] = host;
    }
    else
    {
        PDS_SYS_ERR_LOG("saveMainStationParameter ID: %d", nID);
    }
    ConfigService::instance().setI2(hosts);

    QString strOpUserName = getUserName(request);
    safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_MAINSTATION, QDateTime::currentDateTime(), "");

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}


/************************************************
 * 功能:  获取主站配置参数
 * 输入参数:  request -- http请求
 *      response -- 响应
 ************************************************/
void CommandService::getMainStationParameter( HttpRequest &request, HttpResponse &response )
{
    int nID = request.getParameter("id").toInt();
    I2Set host;

    QList<I2Set> i2Sets = ConfigService::instance().getI2Set();
    if(i2Sets.size() < 2)
    {
        PDS_SYS_ERR_LOG("I2Set size error");
        return;
    }
    if ( nID == 0 )
    {
        host = i2Sets[0];
    }
    else
    {
        host = i2Sets[1];
    }

    QJsonObject data;
    data.insert( "cagIp", host.strIP );
    data.insert( "cagPort", (int)host.nPort );
    data.insert( "cagUri", host.strUrl );
    data.insert( "user", host.strUser );
    data.insert( "password", host.strPasswd );
    data.insert( "dataAcquireTime", (int)host.nHeartInterval );
    data.insert( "heartBeatTime", (int)host.nDataInterval );
    data.insert( "netState", (int)host.netStatus );
    data.insert("auxRtuID", ConfigService::instance().auxCtrlTerminalInfo().strID);
    data.insert("iedRtuID",  ConfigService::instance().getIedRtuID());
    responseWrite( QJsonDocument(encapsulationData( data )).toJson());
}

/************************************************
 * 功能:  获取审计数据
 ************************************************/
void CommandService::getAuditData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    int page = request.getParameter(STR_PAGE).toInt();
    int size = request.getParameter(STR_SIZE).toInt();
    int findType = request.getParameter(STR_AUDIT_FIND_STYLE).toInt();
    QList<safe::AuditData> listData;
    if(findType == 0) //全量查找
    {
        listData = safe::AuditManager::instance().findAllAudits();
    }
    else if(findType == 1) // 按类型查找
    {
        safe::AuditDataType dataType = (safe::AuditDataType)request.getParameter(STR_AUDIT_DATA_TYPE).toInt();
        listData = safe::AuditManager::instance().findAuditsByType(dataType);
    }
    else if(findType == 2) // 按照日期查找
    {
        auto a = request.getParameter(STR_BEGIN_DATE);
        QDateTime startTime =  QDateTime::fromString(request.getParameter(STR_BEGIN_DATE), STR_DATE_TIME_QSTRING_CNA);
        QDateTime endTime = QDateTime::fromString(request.getParameter(STR_END_DATE), STR_DATE_TIME_QSTRING_CNA);
        listData = safe::AuditManager::instance().findAuditsByTime(startTime, endTime);
    }

    QJsonObject object = encapsulationData();
    object.insert("total", listData.size());
    listData = listData.mid((page - 1) * size, listData.size() - (page - 1)* size);
    int responseSize = listData.size() >= size ? size : listData.size();
    QJsonArray jsonAllAudit;
    for(int i = 0; i < responseSize; i++)
    {
        QJsonObject jsonAudit;
        jsonAudit.insert("time", listData[i].time.toString("yyyy-MM-dd hh:mm:ss"));
        jsonAudit.insert("account" , listData[i].account);
        jsonAudit.insert("dataType" , listData[i].dataType);
        jsonAudit.insert("content", listData[i].content);
        jsonAudit.insert("opType", listData[i].opType);
        jsonAllAudit.append(jsonAudit);
    }
    object.insert("rows", jsonAllAudit);

    responseWrite(QJsonDocument(object).toJson());

}

/************************************************
 * 函数名:  SaveModbus
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存modbus信息参数
 ************************************************/
void CommandService::SaveModbus(HttpRequest &request, HttpResponse &response)
{

    Q_UNUSED(response);
    Q_UNUSED(request);
    int standModbus = request.getParameter(STR_STAND_MODBUS).toInt();  //stand_modbus
    ConfigService &configService = ConfigService::instance();
    ModbusSetting modbusSetting = configService.modbusSetting();
    modbusSetting.iBaud = request.getParameter(STR_BAURATE).toInt();
    modbusSetting.iDatabit = request.getParameter(STR_DATA_BIT).toInt();
    modbusSetting.strRts = request.getParameter(STR_RTS);
    modbusSetting.iRts = modbusSetting.strRts.toInt();
    modbusSetting.iStopbit = request.getParameter(STR_STOP_BIT).toInt();
    modbusSetting.strDevice = request.getParameter(STR_SERIAL_PORT);
    modbusSetting.strParity = request.getParameter(STR_CHECK_BIT);
    modbusSetting.ucParity = modbusSetting.strParity.at(0).toLatin1();
    modbusSetting.ucModbusAddress = request.getParameter("modbusAddress").toInt();
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_MODBUS_SET, QDateTime::currentDateTime(), "");
    if(!standModbus)
    {
        Modbus485::SerialInfo stSerialInfo;
        stSerialInfo.strDevice = modbusSetting.strDevice;
        stSerialInfo.iBaud = modbusSetting.iBaud;
        stSerialInfo.ucParity = modbusSetting.ucParity;
        stSerialInfo.iDatabit = modbusSetting.iDatabit;
        stSerialInfo.iStopbit = modbusSetting.iStopbit;
        stSerialInfo.iRts = modbusSetting.iRts;

       /* ModbusDataUnit unitaddr(ModbusDataUnit::InputRegisters, ModbusSlaver::HOST_ADDR_INFO, 1);
        unitaddr.setValue(0, uiAddr_t);
        m_pSlave->setData(unitaddr);*/
        emit sigSwitchModbusDevice(stSerialInfo, modbusSetting);
    }
    else
    {
        ConfigService::instance().saveStdModbusSetting(modbusSetting);
    }

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

void CommandService::saveAlarmConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configService = ConfigService::instance();

    bool ok = false;
    int iTaskGrp = request.getParameter(STR_SF6_ALARM_TASK_GROUP).toInt(&ok);
    ADUChannelType chType = ConfigService::instance().getChanTypEnum(request.getParameter(STR_SF6_ALARM_CHANNEL_TYPE));
    AlarmChannel alarmCh;
    WiringMode wiringMode;
    AlarmMode alarmMode;

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    QList<ADUUnitInfo> adus = configService.ADUList();
    bool bTaskGrpExist = false;
    for( int i = 0; i < adus.size(); i++ )
    {
        if(ADU_VAISALADTP145 == adus[i].eType || ADU_WIKA_GDT20 == adus[i].eType
                || ADU_WIKA_GDHT20 == adus[i].eType || ADU_SHQIUQI_SC75D_SF6 == adus[i].eType)
        {
            if(iTaskGrp == adus[i].iTaskGroup)
            {
                bTaskGrpExist = true;
                break;
            }
        }
    }

    if(!bTaskGrpExist)
    {
        eErrorCode = ERROR_CODE_TASK_GROUP_NOT_EXIST;
    }
    else if(!ConfigService::instance().getAlarmChannelEnum(request.getParameter(STR_SF6_ALARM_CHANNEL), alarmCh))
    {
        eErrorCode = ERROR_CODE_INVALID_CHANNEL;
    }
    else if(!ConfigService::instance().getAlarmWiringModeEnum(request.getParameter(STR_SF6_ALARM_WIRING_MODE), wiringMode))
    {
        eErrorCode = ERROR_CODE_INVALID_WIRING_MODE;
    }
    else if(!ConfigService::instance().getAlarmModeEnum(request.getParameter(STR_SF6_ALARM_MODE), alarmMode))
    {
        eErrorCode = ERROR_CODE_INVALID_ALARM_MODE;
    }
    else
    {
        AlarmConfigInfo alarmConfigInfo;
        alarmConfigInfo.iTaskGroup = iTaskGrp;
        alarmConfigInfo.eChannelType = chType;
        alarmConfigInfo.fAlarmThreshold = request.getParameter(STR_SF6_ALARM_THRESHOLD).toFloat();
        alarmConfigInfo.fAlarmRecoveryThershold = request.getParameter(STR_SF6_ALARM_RECOVERY_THRESHOLD).toFloat();
        alarmConfigInfo.eAlarmChannel = alarmCh;
        alarmConfigInfo.strAlarmExternalIOSN = request.getParameter(STR_SF6_ALARM_EXTERNAL_IO_SN);
        alarmConfigInfo.iAlarmExternalIOChannel = request.getParameter(STR_SF6_ALARM_EXTERNAL_IO_CHANNEL).toInt();
        alarmConfigInfo.eWiringMode = wiringMode;
        alarmConfigInfo.eAlarmMode = alarmMode;
        alarmConfigInfo.iAlarmTime = request.getParameter(STR_SF6_ALARM_TIME).toInt();
        alarmConfigInfo.iAlarmInterval = request.getParameter(STR_SF6_ALARM_INTERVAL).toInt();
        alarmConfigInfo.iAlarmDuration = request.getParameter(STR_SF6_ALARM_DURATION).toInt();

        AlarmFacade &alarmMng = AlarmFacade::instance();
        if(alarmMng.isAlarmConfigExist(iTaskGrp, chType))
        {
            if(alarmMng.updateAlarmConfig(iTaskGrp, chType, alarmConfigInfo))
            {
                configService.saveAlarmConfigInfo(alarmConfigInfo);
            }
            else
            {
                eErrorCode = ERROR_COLDE_WIRING_MODE_CONFLICT;
            }
        }
        else
        {
            if(alarmMng.addAlarmConfig(alarmConfigInfo))
            {
                configService.saveAlarmConfigInfo(alarmConfigInfo);
            }
            else
            {
                eErrorCode = ERROR_COLDE_WIRING_MODE_CONFLICT;
            }
        }
    }

    if( ERROR_CODE_NONE_ERROR == eErrorCode )
    {
        PDS_SYS_INFO_LOG("Finish to save alarm config");
    }
    else
    {
        PDS_SYS_WARNING_LOG("Fail to save alarm config, error code : %d", eErrorCode);
    }

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

void CommandService::deleteAlarmConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    int iTaskGroup = request.getParameter(STR_SF6_ALARM_TASK_GROUP).toInt();
    ADUChannelType eChannelType = ConfigService::instance().getChanTypEnum(request.getParameter(STR_SF6_ALARM_CHANNEL_TYPE));

    ConfigService &configService = ConfigService::instance();
    PDS_SYS_INFO_LOG("Delete Alarm config, task group : %d, channel type : %d", iTaskGroup, eChannelType);
    configService.deleteAlarmConfig(iTaskGroup, eChannelType);
    AlarmFacade::instance().removeAlarmConfig(iTaskGroup, eChannelType);

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  SwitchUser
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:确认用户名密码
 ************************************************/
void CommandService::SwitchUser(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QJsonObject jsonUserInfo;
    HTTPErrorCode errCode = ERROR_CODE_UNKONOW;

    if(!isIllegalUser(request, response))
    {
        responseWrite(QJsonDocument(encapsulationData(jsonUserInfo, ERROR_COLDE_ILLEGAL_USER)).toJson());
        return;
    }

    QString strName;
    QString strWord;
    safe::UserManager::instance().rasDecrypt(request.getParameter(STR_USER_NAME), strName, true);
    safe::UserManager::instance().rasDecrypt(request.getParameter(STR_PASSWORD), strWord, true);

    if(!strName.isEmpty())
    {
        safe::AuditManager::instance().addAudit(strName, safe::AUDIT_LOGIN_IN, QDateTime::currentDateTime());
    }

    //判断是否已经登录过
    QMap<QByteArray,QByteArray> mapCookies = request.getCookieMap();
    HttpSession session =  sessionStore->getSession(request, response);
    QString cookieName = "";
    QString userName = "";
    QMapIterator<QByteArray, QByteArray> i(mapCookies);
    while (i.hasNext())
    {
        QString key = i.next().key();
        if(key == "cookieRan")
        {
            cookieName = i.value();
        }
        else if(key == STR_USER_NAME)
        {
            userName = i.value();
        }
    }
    QString sessionName = "";
    if(cookieName != "")
    {
        sessionName = session.get(cookieName.toLatin1()).toString();
    }
    safe::UserInfo userInfo;
    if(sessionName == "" )
    {
        errCode = ERROR_COLDE_ILLEGAL_USER;
    }
    else
    {
        safe::OpResult opResult  = safe::UserManager::instance().login(strName, strWord, "", userInfo);
        errCode = getCodeFromSafe(opResult);
    }
    if(ERROR_CODE_NONE_ERROR == errCode)
    {
        setCookieSession(strName, request, response);
    }
    loginResponse(userInfo, errCode, jsonUserInfo);

    responseWrite(QJsonDocument(encapsulationData(jsonUserInfo)).toJson());
}

/************************************************
 * 函数名:  GetADUType
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端类型
 ************************************************/
void CommandService::GetADUType(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others otherData;
    otherData.GetADUType();
    responseWrite(QJsonDocument(encapsulationData(otherData)).toJson());
}

/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能：更改用户密码
 ************************************************/
void CommandService::ChangeUserPas(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);


    QMap<QByteArray,QByteArray> mapCookies = request.getCookieMap();
    HttpSession session =  sessionStore->getSession(request, response);
    QString cookieName = "";
    QString userName = "";
    QMapIterator<QByteArray, QByteArray> i(mapCookies);
    while (i.hasNext())
    {
        QString key = i.next().key();
        if(key == "cookieRan")
        {
            cookieName = i.value();
        }
        else if(key == STR_USER_NAME)
        {
            userName = i.value();
        }
    }
    QString sessionName = "";
    if(cookieName != "")
    {
        sessionName = session.get(cookieName.toLatin1()).toString();
    }

    safe::AuditManager::instance().addAudit(sessionName, safe::AUDIT_LOGIN_OUT, QDateTime::currentDateTime());
    if(sessionName == "" )
    {
        responseWrite(QJsonDocument(encapsulationData(ERROR_COLDE_ILLEGAL_USER)).toJson());
        return;
    }

    QString strName;
    QString strOldPasWord;
    QString strNewPasWord;
    QString operateName;
    safe::UserManager::instance().rasDecrypt(request.getParameter(STR_USER_NAME), strName, true);
    safe::UserManager::instance().rasDecrypt(request.getParameter("oldPassword"), strOldPasWord, true);
    safe::UserManager::instance().rasDecrypt(request.getParameter("newPassword"), strNewPasWord, true);
    safe::UserManager::instance().rasDecrypt(sessionName, operateName, true);
    safe::OpResult result = safe::UserManager::instance().changePass(sessionName, strName, strNewPasWord, strOldPasWord);
    HTTPErrorCode errCode = getCodeFromSafe(result);
    responseWrite(QJsonDocument(encapsulationData(errCode)).toJson());

    safe::AuditManager::instance().addAudit(strName, safe::AUDIT_LOGIN_OUT, QDateTime::currentDateTime());

}



/************************************************
 * 函数名:  GetSyncDataADUType
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取数据同步前端类型
 ************************************************/
void CommandService::GetSyncDataADUType(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others otherData;
    otherData.GetSyncDataADUType();
    responseWrite(QJsonDocument(encapsulationData(otherData)).toJson());
}

/************************************************
 * 函数名:  syncAduData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 数据同步前端
 ************************************************/
void CommandService::syncAduData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigData data;
    QString aduList =  request.getParameter(STR_ADU_LIST);
    PDS_SYS_INFO_LOG("Begin to sync adu : %s", aduList.toLatin1().data());
    QStringList strListadu = aduList.split(",");
    ADUType aduType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    bool bRet = data.syncData(strListadu, aduType);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    if (!bRet)
    {
        eErrorCode = ERROR_CODE_UPDATE_ADU_ADU_ID_ERROR;
        PDS_SYS_WARNING_LOG("Fail to sync adu %s", aduList.toLatin1().data());
    }
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}

/************************************************
 * 函数名:  getSystemConfig
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取系统设置选项
 ************************************************/
void CommandService::getSystemConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData data;
    data.SystemSettingConfig();
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  GetNetworkInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取网络信息
 ************************************************/
void CommandService::GetNetworkInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData data;
    data.NetworkSetting();
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  告警确认(安全测评版本)
 ************************************************/
void CommandService::SafeAlarmConfirm(HttpRequest &request, HttpResponse &response)
{
#ifdef USE_SAFE
    Q_UNUSED(response)
    Q_UNUSED(request)

    quint16 alarmId = request.getParameter("alarmId").toInt();
    PDSSAFE_W::WarnFactory::instance().warnConfirm(alarmId);

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
#endif
}

/************************************************
  * 输入参数:  request -- http请求
  *      response -- 响应
  * 输出参数:  NULL
  * 返回值:  NULL
  * 功能:  获取审计数据(安全测评版本)
  ************************************************/
void CommandService::GetAudit(HttpRequest &request, HttpResponse &response)
{
    //     Q_UNUSED(response)
    //     Q_UNUSED(request)
    //     PDSSAFE_A::AuditDataType auditType = (PDSSAFE_A::AuditDataType)(request.getParameter("auditDataType").toInt());
    //     int page = request.getParameter(STR_PAGE).toInt();
    //     int size = request.getParameter(STR_SIZE).toInt();
    //     quint16 begin = (page-1) * size;
    //     quint16 end = (page) * (size);
    //     QList<PDSSAFE_A::AuditData> listData;

    //     if(auditType == PDSSAFE_A::ADUITTYPE_INVALID)
    //     {
    //         listData =  AuditManager::instance().findAllAudits();
    //     }
    //     else
    //     {
    //         qDebug() << "AuditManager::instance().findAuditsByType" << auditType;
    //         listData =  AuditManager::instance().findAuditsByType(auditType);
    //     }

    //     QList<PDSSAFE_A::AuditData> listAuditData;
    //     for(int i = begin; i < end; i++)
    //     {
    //         if(i < listData.size())
    //         {
    //             listAuditData.append(listData[i]);
    //         }
    //         else
    //         {
    //             break;
    //         }
    //     }

    //     QJsonObject jsonInfo;
    //     QJsonArray jsonArrAlarm;
    //     for (int i = 0; i < listAuditData.size(); i++)
    //     {
    //         QJsonObject jsonAudit;
    //         jsonAudit.insert("userName", listAuditData.at(i).account);
    //         jsonAudit.insert("opType", listAuditData.at(i).opType);
    //         jsonAudit.insert("dataType", listAuditData.at(i).dataType);
    //         jsonAudit.insert("time", listAuditData.at(i).time.toString("yyyy-MM-dd hh:mm:ss"));
    //         jsonArrAlarm.append(jsonAudit);
    //     }
    //     jsonInfo.insert("rows", jsonArrAlarm);
    //     jsonInfo.insert("total", listData.size());
    //     responseWrite(QJsonDocument(encapsulationData(jsonInfo)).toJson());
}


/************************************************
  * 输入参数:  request -- http请求
  *      response -- 响应
  * 输出参数:  NULL
  * 返回值:  NULL
  * 功能:  设置告警参数(安全测评版本)
  ************************************************/
void CommandService::setSafeAlarmPara(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)
    ADUChannelType chType = (ADUChannelType)request.getParameter(STR_CHANNEL_TYPE).toInt();
    float earlyAlarmValue  = request.getParameter("warnValueParam").toFloat();
    float alarmValue = request.getParameter("alarmValueParam").toFloat();
    ConfigService::instance().saveSafeAlarm(chType, alarmValue, earlyAlarmValue);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

/************************************************
  * 输入参数:  request -- http请求
  *      response -- 响应
  * 输出参数:  NULL
  * 返回值:  NULL
  * 功能:  获取告警参数(安全测评版本)
  ************************************************/
void CommandService::getSafeAlarmPara(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)
    ADUChannelType chType = (ADUChannelType)request.getParameter(STR_CHANNEL_TYPE).toInt();
    float alarmValue  = 0.f;
    float earlyAlarmValue= 0.f;
    ConfigService::instance().safeAlarmConfig(chType, alarmValue, earlyAlarmValue);

    QJsonObject jsonInfo;
    jsonInfo.insert(STR_CHANNEL_TYPE, chType);
    jsonInfo.insert("alarmValue", alarmValue);
    jsonInfo.insert("earlyAlarmValue", earlyAlarmValue);

    responseWrite(QJsonDocument(encapsulationData(jsonInfo)).toJson());
}

/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取告警(安全测评版本)
 ************************************************/
void CommandService::GetSafeAlarm(HttpRequest &request, HttpResponse &response)
{
#ifdef USE_SAFE
    Q_UNUSED(response)
    Q_UNUSED(request)

    int page = request.getParameter(STR_PAGE).toInt();
    int size = request.getParameter(STR_SIZE).toInt();
    quint16 begin = (page-1) * size;
    quint16 end = (page) * (size);
    ADUChannelType chType = (ADUChannelType)request.getParameter(STR_CHANNEL_TYPE).toInt();
    PDSSAFE_W::WarnState state = ( PDSSAFE_W::WarnState)request.getParameter("alarmState").toInt();
    PDSSAFE_W::WarnLevel levle = (PDSSAFE_W::WarnLevel)request.getParameter("alarmLevel").toInt();
    QList<PDSSAFE_W::WarnInfo>listInfo =  PDSSAFE_W::WarnFactory::instance().getWarn(begin, end, chType, state, levle);

    QList<PDSSAFE_W::WarnInfo> listWarnData;
    for(int i = begin; i < end; i++)
    {
        if(i < listInfo.size())
        {
            listWarnData.append(listInfo[i]);
        }
        else
        {
            break;
        }
    }

    QJsonObject jsonInfo;
    QJsonArray jsonArrAlarm;
    for (int i = 0; i < listWarnData.size(); i++)
    {
        QJsonObject jsonAlarm;
        jsonAlarm.insert(STR_POINT_NAME, listInfo.at(i).pointName);
        jsonAlarm.insert(STR_ADU_ID, listInfo.at(i).aduID.toString());
        jsonAlarm.insert(STR_CHANNEL_TYPE, listInfo.at(i).channelType);
        jsonAlarm.insert("warnLevel", listInfo.at(i).warnLevel);
        jsonAlarm.insert("warnState", listInfo.at(i).warnState);
        jsonAlarm.insert("warnValue", listInfo.at(i).warnValue.toFloat());
        jsonAlarm.insert("alarmId", (int)listInfo.at(i).index);
        jsonAlarm.insert("alarmTime", listInfo.at(i).dateTime.toString("yyyy-MM-dd hh:mm:ss"));
        jsonArrAlarm.append(jsonAlarm);
    }
    jsonInfo.insert("rows", jsonArrAlarm);
    jsonInfo.insert("total", listInfo.size());
    responseWrite(QJsonDocument(encapsulationData(jsonInfo)).toJson());
#endif
}


/************************************************
 * 函数名:  saveNetWorkInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存网络信息参数
 ************************************************/
void CommandService::SaveNetWorkInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    bool bret = true;
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    ConfigService &configService = ConfigService::instance();
    WebSetting webSetting = configService.webSetting();
    //int iServerPort = webSetting.iServerPort;
    webSetting.iServerPort = request.getParameter(STR_SERVER_PORT).toInt();
    //QString strServerAddress = webSetting.strServerAddress;
    webSetting.strServerAddress = request.getParameter(STR_SERVER_IP);

    WebType eWebType = webSetting.eWebType;
    Q_UNUSED(eWebType)

    webSetting.eWebType = getWebTypeFormString(request.getParameter(STR_NETWORK_NAME));
    QString strAPN(request.getParameter(STR_NETWORK_APN));
    webSetting.strWorkAPN = strAPN; //request.getParameter(STR_NETWORK_APN);
    webSetting.strNetworkUserName = request.getParameter(STR_NETWORK_USERNAME);
    webSetting.strNetworkPassword = request.getParameter(STR_NETWORK_PASSWORD);
    webSetting.strSNTPAddress = request.getParameter(STR_NTP_IP);
    webSetting.strSNTPPort = request.getParameter(STR_NTP_PORT);
    webSetting.bStrategy4G = request.getParameter(STR_4G_STRATEGY).toInt();

    QString audit = "4G";
    if(WEB_TYPE_3G == webSetting.eWebType)
    {
        audit = "3G";
    }
    else if(WEB_TYPE_WIRED_NETWORK == webSetting.eWebType)
    {
        audit = "network";
    }

    auto paraMap = request.getParameterMap();
    auto itr = paraMap.cend();
    if ((itr = paraMap.find(STR_DEVICE_IP)) != paraMap.cend())
    {
        webSetting.strIP = itr.value();
    }
    webSetting.strSubnetMask = "*************";
    if(!webSetting.strIP.indexOf("192.168.4."))   //ip网段重叠
    {
        responseWrite(QJsonDocument(encapsulationData((HTTPErrorCode)(fCode::FCODE_S_CONFIG_SAMEIP))).toJson());
        return;
    }
    else
    {
        QStringList  listIp = webSetting.strIP.split(".");
        if(listIp.size() != 4)
        {
            PDS_SYS_ERR_LOG("IP error", webSetting.strIP.toLatin1().data());
            // responseWrite(QJsonDocument(encapsulationData(fCode::FCODE_S_CONFIG_SAMEIP)).toJson());
        }
        else
        {
            listIp[listIp.size() - 1] = "1";
            webSetting.strGateWay = listIp.join(".");
        }

        //safe::AuditManager::instance().addAudit("user", safe::AUDIT_NET_SET, QDateTime::currentDateTime(), audit);

        configService.saveWebSetting(webSetting);

        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
    }


}

/************************************************
 * 函数名:  GetNetworkIConfig
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取网络信息参数
 ************************************************/
void CommandService::GetNetworkConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigData data;
    data.NetworkSettingConfig();
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());

}

/************************************************
 * 函数名:  ExportDBData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  导出数据库数据
 ************************************************/
void CommandService::ExportDBData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);

    quint8 ucExportType = request.getParameter("type").toInt();
    QString strExportPath = request.getParameter("exportPath");
    HTTPErrorCode eErrorCode = ERROR_CODE_DB_EXPORT_NOT_INSERT_UDISK;
    QJsonObject data;
    if (UDiskInfo::instance().getUDiskIsInsert())
    {
        DBExportErrorCode eDBExportErrorCode = DB_EXPORT_OTHER_ERROR;
        if (ucExportType == 0)
        {
            QDate startTime;
            ConfigService::instance().getExportDBStartDate(startTime);
            QDate endTime = QDate::currentDate();
            eDBExportErrorCode = DBServer::instance().exportDB(strExportPath, startTime, endTime);
            if (eDBExportErrorCode == DB_EXPORT_SUCCESSED)
            {
                ConfigService::instance().saveExportDBEndDate(endTime);
            }
        }
        else if (ucExportType == 1)
        {
            eDBExportErrorCode = DBServer::instance().exportAllData(strExportPath);
        }
        else if (ucExportType == 2)
        {
            QDate startTime = QDate::fromString(request.getParameter(STR_BEGIN_TIME),STR_DATE_QSTRING_CNA);
            QDate endTime = QDate::fromString(request.getParameter(STR_END_TIME),STR_DATE_QSTRING_CNA);
            eDBExportErrorCode = DBServer::instance().exportDB(strExportPath, startTime, endTime);
            if (eDBExportErrorCode == DB_EXPORT_SUCCESSED)
            {
                ConfigService::instance().saveExportDBEndDate(endTime);
            }
        }
        else
        {
        }

        switch (eDBExportErrorCode) {
        case DB_EXPORT_SUCCESSED:
            eErrorCode = ERROR_CODE_NONE_ERROR;
            break;
        case DB_EXPORT_FILE_PATH_ERROR:
            eErrorCode = ERROR_CODE_DB_EXPORT_FILE_PATH_ERROR;
            break;
        case DB_EXPORT_TIME_ERROR:
            eErrorCode = ERROR_CODE_DB_EXPORT_TIME_ERROR;
            break;
        case DB_EXPORT_NO_DATE:
            eErrorCode = ERROR_CODE_DB_EXPORT_NO_DATE;
            break;
        case DB_EXPORT_USB_DRIVER_FULL:
            eErrorCode = ERROR_CODE_DB_EXPORT_USB_DRIVER_FULL;
            break;
        case DB_EXPORT_OTHER_ERROR:
            eErrorCode = ERROR_CODE_DB_EXPORT_NOT_INSERT_UDISK;
            break;
        case DB_EXPORT_NO_STATION_CONFIG:
            eErrorCode = ERROR_CODE_DB_EXPORT_NO_STATION_CONFIG;
            break;

        default:
            break;
        }
        data.insert(STR_FILE, strExportPath);
    }
    else
    {
        PDS_SYS_WARNING_LOG("Fail to export db data, usb disk has been removed");
    }
    responseWrite(QJsonDocument(encapsulationData(data, eErrorCode)).toJson());

}

/************************************************
 * 函数名:  GetExportDataPath
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取数据导出路径
 ************************************************/
void CommandService::GetExportDataPath(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QJsonObject date;
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    if (UDiskInfo::instance().getUDiskIsInsert())
    {
        date.insert(STR_FILE,UDiskInfo::instance().getUDiskPath());
    }
    else
    {
        eErrorCode = ERROR_CODE_DB_EXPORT_NOT_INSERT_UDISK;
    }
    responseWrite(QJsonDocument(encapsulationData(date, eErrorCode)).toJson());
}


/************************************************
 * 函数名:  DeleteStation
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除站点
 ************************************************/
void CommandService::DeleteStation(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService::instance().delStation();
    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  AttentionShutdown
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  定时关机响应
 ************************************************/
void CommandService::AttentionShutdown(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)
    quint8 ucIsMonitorShutDown = request.getParameter(STR_SHUT_DOWN_TYPE).toInt();
    PDS_SYS_INFO_LOG("AttentionShutdown: %d", ucIsMonitorShutDown);
    emit sigMonitorShutDown((bool)ucIsMonitorShutDown);
    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  WriteLogInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  记录日志
 ************************************************/
void CommandService::WriteLogInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    QString strLogInfo = request.getParameter("loginfo");
    //通知页面的操作结果
    responseWrite(QJsonDocument(encapsulationData()).toJson());

}


/************************************************
 * 函数名:  GetCommonType
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通用枚举信息
 ************************************************/
void CommandService::GetCommonType(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    CommonEnumData eEnumData = (CommonEnumData)request.getParameter(STR_TYPE).toInt();

    Others data;
    switch (eEnumData) {
    case VOL_LEVEL_ENUM:
        data.getVolLevelList();
        break;
    case DEVICE_TYPE_ENUM:
        data.getDeviceTypeList();
        break;
    case ADU_TYPE_ENUM:
        data.getaduTypeList();
        break;
    case LINK_TYPE_ENUM:
        data.getLinkTypeList();
        break;
    case CHANNEL_TYPE_ENUM:
        data.getChannelTypeList();
        break;
    case RS485_PORT_ENUM:
        data.getRS485ComPortList();
        break;
    case ADU_WORK_TYPE_ENUM:
        data.getADUWorkModeList();
        break;
    case ADU_LOWER_POWER_WAKE_UP_SPACE:
        data.getLowerPowerWakeIpSpace();
        break;
    default:

        break;
    }
    //通知页面的操作结果
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  GetADULIst
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端列表
 ************************************************/
void CommandService::GetADUList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others data;
    data.getADUList();

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

void CommandService::getAlarmConfigTreeList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    Others data;
    data.getAlarmConfigTreeList();

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  GetADUInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端详情
 ************************************************/
void CommandService::GetADUInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);

    ConfigData data;
    data.GetADUInfo(strADUID);
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}


/************************************************
 * 输入参数:
 *  request -- http请求
 * 功能:  保存温湿度传感器
 ************************************************/
bool CommandService::saveTempHumADU(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_ADU_SET, QDateTime::currentDateTime(), strADUID);
    QString strADUOldID = request.getParameterMap().contains(STR_ADU_OLD_ID) ?
                request.getParameter(STR_ADU_OLD_ID).toUpper() :
                strADUID;
    //    QString strADUOldID = request.getParameter(STR_ADU_OLD_ID).toUpper();

    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    ADUUnitInfo adu;

    ConfigService::instance().getADU(strADUID, eADUType, adu);
    adu.strID = strADUID;
    adu.eType = eADUType;
    adu.strName = request.getParameter(STR_ADU_NAME);
    adu.eLinkGroup = (Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)));
    adu.strRS485ComPort = request.getParameter(STR_ADU_RS485_COM_PORT);
    adu.iTaskGroup = request.getParameter(STR_SF6_ALARM_TASK_GROUP).toInt();

    adu.stADUParam.ucFrequency = request.getParameter(STR_FREQUNCY).toInt();
    adu.stADUParam.ucWorkGroup = request.getParameter(STR_ADU_WORK_GROUP).toInt();
    adu.stADUParam.ucConnectionLoad = request.getParameter(STR_ADU_COMM_LOAD).toInt();
    adu.stADUParam.ucConnectionSpeed = request.getParameter(STR_ADU_COMM_SPEED).toInt();
    adu.stADUParam.uiSleepTime = request.getParameter(STR_ADU_SLEEP_TIME).toInt();
    adu.stADUParam.uiSampleSpace = request.getParameter(STR_ADU_SAMPLE_SPACE).toInt();
    adu.stADUParam.usStartSampleTime = request.getParameter(STR_ADU_SAMPEL_START_TIMR).toInt();
    adu.stADUParam.ucAutoUpdate = request.getParameter(STR_ADU_B_AUTO_GIVING).toInt();
    adu.stADUParam.eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
    adu.stADUParam.uiAutoGivingSpace = request.getParameter(STR_ADU_AUTO_GIVISPACE).toInt();
    adu.stADUParam.eLinkGroup = adu.eLinkGroup;
    if ( Monitor::WORKER_MODEL_LOWPOWER == adu.stADUParam.eADUWorkModel )
    {
        adu.stADUParam.ucStartArtificialTime = 8;
        adu.stADUParam.ucEndArtificialTime = 20;
        adu.stADUParam.usArtificialWakeUpInterval = 3;//request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
        adu.stADUParam.usNotArtificialWalkUpInterval = 30;//request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
        adu.stADUParam.bAutoChangeMode = 1;//request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE).toInt();
    }
#ifndef Q_WS_QWS
    ConfigService::instance().saveADUInfo(adu);
#endif
    ConfigService::instance().saveADUName(adu.strID, adu.strName);

    //前端存在，此时为修改前端
    if(ConfigService::instance().isADUIDExisted(strADUOldID))
    {
        if(strADUOldID != strADUID)  //更改了前端ID
        {
            ConfigService::instance().setAduID(strADUOldID, strADUID);
        }
    }
    else //前端不存在，按照默认参数本地存储,防止后续操作找不到前端
    {
        ADUUnitInfo defaultAdu = adu;
        defaultAdu.stADUParam.uiSleepTime = 1;
        defaultAdu.stADUParam.eADUWorkModel = Monitor::WORKER_MODEL_MAINTAIN;
        ConfigService::instance().addADUInfo(defaultAdu);
        adu.stADUParam.usNumInGroup = defaultAdu.stADUParam.usNumInGroup;
    }

    MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam);

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

    return true;
}


/*************************************************
输入参数：userName 用户名
        request -- http请求
        response --http返回
说明：设置用户cookie和session
*************************************************************/
void CommandService::setCookieSession(const QString &userName, HttpRequest &request, HttpResponse &response)
{
    //设置cookie,用户名和随机时间戳
    HttpCookie cookie;
    cookie.setName(STR_USER_NAME);
    cookie.setValue(userName.toLatin1());
    response.setCookie(cookie);
    HttpCookie cookieInfo;
    QDateTime dataTime = QDateTime::currentDateTime();
    QString strTime =  QString::number(dataTime.toTime_t());
    cookieInfo.setName("cookieRan");
    cookieInfo.setValue(strTime.toLatin1());
    response.setCookie(cookieInfo);

    //设置session
    HttpSession session = sessionStore->getSession(request, response, true);
    session.set(strTime.toLatin1(), userName.toLatin1());
}

/*************************************************
输入参数：userInfo 用户信息
        loginResult 登录结果
        jsonObject 登录结果JSON
说明：生成登录结果JSON
*************************************************************/
void CommandService::loginResponse(safe::UserInfo &userInfo, const HTTPErrorCode loginResult, QJsonObject &jsonObject)
{
    if(ERROR_CODE_NONE_ERROR == loginResult)
    {
        jsonObject.insert(STR_ISADUTORITY, "loginSuccess");
        QJsonObject userInfoObject;
        userInfoObject.insert(STR_USERSTATE, userInfo.stState);
        userInfoObject.insert(STR_LANGUAGE, userInfo.eLanguage);
        userInfoObject.insert(STR_FIRSTLOGIN, userInfo.bFirstLogin);
        userInfoObject.insert(STR_CHARATYPE, userInfo.eCharaType);
        userInfoObject.insert(STR_PASPAST, userInfo.bPasPast);
        QJsonArray arrJson;
        for(auto i = 0; i < userInfo.listOpType.size(); i++)
        {
            arrJson.append(userInfo.listOpType[i]);
        }
        userInfoObject.insert(STR_USERPERMISSION, arrJson);
        jsonObject.insert(STR_USERINFO,  userInfoObject);
    }
    else
    {
        jsonObject.insert(STR_ISADUTORITY, "loginFail");
    }
}

/************************************************
 * 函数名:  saveADUInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存前端详情
 ************************************************/
void CommandService::saveADUInfo(HttpRequest &request, HttpResponse &response)
{
    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    if(eADUType == ADU_TEMP_HUM_IS)  //温湿度传感器
    {
        saveTempHumADU(request, response);
    }
    else
    {
        QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");
        QString strADUOldID = request.getParameter(STR_ADU_OLD_ID).toUpper();
        //safe::AuditManager::instance().addAudit("user", safe::AUDIT_ADU_SET, QDateTime::currentDateTime(), strADUID);
        HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
        if ((strADUOldID != strADUID) && ConfigService::instance().isADUIDExisted(strADUID) )
        {
            eErrorCode = ERROR_CODE_CONFIG_ADU_ID_EXISTED;   //前端已经存在
        }
        else if (!strADUOldID.isEmpty() && !ConfigService::instance().isADUIDExisted(strADUOldID))
        {
            eErrorCode = ERROR_CODE_CONFIG_ADU_ID_NOT_EXISTED;  //前端不存在

        }
        else
        {
            ADUUnitInfo adu;

            ConfigService::instance().setAduID(strADUOldID, strADUID);
            bool bIsExist = ConfigService::instance().getADU(strADUID, eADUType, adu);
            adu.strName = request.getParameter(STR_ADU_NAME);
            adu.eType = eADUType;
            adu.strID = strADUID;
            adu.eLinkGroup = (Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)));
            adu.strRS485ComPort = request.getParameter(STR_ADU_RS485_COM_PORT);
            adu.iTaskGroup = request.getParameter(STR_SF6_ALARM_TASK_GROUP).toInt();
            adu.stADUParam.ucFrequency = request.getParameter(STR_FREQUNCY).toInt();
            adu.stADUParam.ucWorkGroup = request.getParameter(STR_ADU_WORK_GROUP).toInt();
            adu.stADUParam.ucConnectionLoad = request.getParameter(STR_ADU_COMM_LOAD).toInt();
            adu.stADUParam.ucConnectionSpeed = request.getParameter(STR_ADU_COMM_SPEED).toInt();

            //传感器休眠模式：1，定时休眠；0，不休眠
            if(request.getParameterMap().contains(STR_ADU_SLEEP_TIME))
            {
                adu.stADUParam.uiSleepTime = request.getParameter(STR_ADU_SLEEP_TIME).toInt();
            }
            else
            {
                adu.stADUParam.uiSleepTime = 1;
            }
            //传感器工作模式：默认维护模式
            if(request.getParameterMap().contains(STR_ADU_MODE))
            {
                adu.stADUParam.eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
            }
            else
            {
                adu.stADUParam.eADUWorkModel = Monitor::WORKER_MODEL_MAINTAIN;
            }

            adu.stADUParam.uiSampleSpace = request.getParameter(STR_ADU_SAMPLE_SPACE).toInt();
            adu.stADUParam.usStartSampleTime = request.getParameter(STR_ADU_SAMPEL_START_TIMR).toInt();
            adu.stADUParam.ucAutoUpdate = request.getParameter(STR_ADU_B_AUTO_GIVING).toInt();

            adu.stADUParam.uiAutoGivingSpace = request.getParameter(STR_ADU_AUTO_GIVISPACE).toInt();
            adu.stADUParam.extendInfo =  request.getParameter(STR_ADU_EXTEND);
            adu.stADUParam.eAduFilterNoiseMode = (AduFilterNoiseMode)request.getParameter("thresholdMode").toInt();
            adu.stADUParam.fUesdThreshold = request.getParameter("threshold").toFloat() / 100;
            if ( Monitor::WORKER_MODEL_LOWPOWER == adu.stADUParam.eADUWorkModel )
            {
                adu.stADUParam.ucStartArtificialTime = request.getParameter(STR_ADU_ARTIFICIAL_START_TIME).toInt();
                adu.stADUParam.ucEndArtificialTime = request.getParameter(STR_ADU_ARTIFICIAL_END_TIME).toInt();
                adu.stADUParam.usArtificialWakeUpInterval = request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
                adu.stADUParam.usNotArtificialWalkUpInterval = request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
                adu.stADUParam.bAutoChangeMode = request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE).toInt();
                adu.stADUParam.uiSleepTime = 1; //低功耗模式下，默认定时休眠
            }

            adu.stADUParam.eLinkGroup = adu.eLinkGroup;
            ConfigService::instance().saveADUName( adu.strID, adu.strName);

            if (!bIsExist)   //前端不存在, 添加传感器
            {
                // 保存前端名及链路信息
                adu.stADUParam.uiSleepTime = 1;
                ConfigService::instance().addADUInfo(adu);
                if(MoistureController::isMoistureAdu(adu.eType))
                {
                    MoistureController::instance().addTransmitter(adu);
                }

                sendManualOperatorSignal(adu.strID, Monitor::ManualOpreationType::ADD_OPERATION,
                                         (adu.stADUParam.uiSleepTime == 0) ? Monitor::AduModeType::MODEL_0 : Monitor::AduModeType::MODEL_1);
            }
            else
            {
                sendManualOperatorSignal(adu.strID, Monitor::ManualOpreationType::EDIT_OPERATION);
            }

            if (ConfigService::instance().getISADUType(adu.eType))
            {
                if(!bIsExist)
                {
                    adu.stADUParam.uiSleepTime  = request.getParameter(STR_ADU_SLEEP_TIME).toInt();
                }

                if(ADU_UHF_IS == adu.eType && bIsExist)
                {
                    ADUUnitInfo aduInfoTmp;
                    ConfigService::instance().getADU(strADUID, aduInfoTmp);
                    {
                        bool bIsAduParamSame = (aduInfoTmp.stADUParam.eADUWorkModel == adu.stADUParam.eADUWorkModel) &&
                                (aduInfoTmp.stADUParam.uiSleepTime == adu.stADUParam.uiSleepTime) &&
                                (aduInfoTmp.stADUParam.ucWorkGroup == adu.stADUParam.ucWorkGroup) &&
                                (aduInfoTmp.stADUParam.ucAutoUpdate == adu.stADUParam.ucAutoUpdate) &&
                                (aduInfoTmp.stADUParam.ucFrequency == adu.stADUParam.ucFrequency) &&
                                (aduInfoTmp.stADUParam.uiSampleSpace == adu.stADUParam.uiSampleSpace) &&
                                (aduInfoTmp.stADUParam.usStartSampleTime == adu.stADUParam.usStartSampleTime);

                        aduInfoTmp.stADUParam.eAduFilterNoiseMode = adu.stADUParam.eAduFilterNoiseMode;
                        aduInfoTmp.stADUParam.fUesdThreshold = adu.stADUParam.fUesdThreshold;
                        ConfigService::instance().saveADUInfo(aduInfoTmp);
                        if(!bIsAduParamSame)
                        {
                           MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam);
                        }
                    }
                }
                else
                {
                    MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam);
                }
            }
            else
            {
                ConfigService::instance().saveADUInfo(adu);
                if(MoistureController::isMoistureAdu(adu.eType))
                {
                    MoistureController::instance().updateTransmitterParam(strADUID, adu);
                }
            }
        }
        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
    }
}



/************************************************
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 功能: 添加普通用戶
 ************************************************/
void CommandService::addNormalUser(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    QString strOpUserName = getUserName(request);
    QString strUserName = request.getParameter(STR_USER_NAME);
    QString strPas = "Pdnormal@2019";
    safe::UserPasData userPasData;
    userPasData.strPassWord = strPas;
    safe::UserLoginData loginData;
    safe::OpResult opResult = safe::UserManager::instance().addNormalUser(strOpUserName, strUserName, userPasData, loginData);

    QJsonObject info;
    if(safe::OP_RESULT_SUCCESS == opResult)
    {
        info.insert("result", true);
    }
    else
    {
        info.insert("result", false);
    }

    responseWrite(QJsonDocument(encapsulationData(info)).toJson());

}

/************************************************
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 功能: 删除普通用户
 ************************************************/
void CommandService::deleteNormalUser(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    QString strOpUserName = getUserName(request);
    QString strUserName = request.getParameter(STR_USER_NAME);
    safe::OpResult opResult = safe::UserManager::instance().deleteUser(strOpUserName, strUserName);

    QJsonObject info;
    if(safe::OP_RESULT_SUCCESS == opResult)
    {
        info.insert("result", true);
    }
    else
    {
        info.insert("result", false);
    }

    responseWrite(QJsonDocument(encapsulationData(info)).toJson());
}

/************************************************
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 功能: 获取普通用户列表
 ************************************************/
void CommandService::getNormalUserList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    QString strOpUserName = getUserName(request);
    QList<safe::UserInfo> listInfo;
    safe::UserManager::instance().getUserInfo(strOpUserName, safe::CTYPE_NORMAL, listInfo);

    ConfigData data;
    data.getNormalUser(listInfo);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  deleteADUInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除前端
 ************************************************/
void CommandService::deleteADUInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);

    ADUType eType;
    bool bExist = ConfigService::instance().getADUTypeFromID(strADUID, eType);
    HTTPErrorCode eErrorCode = (HTTPErrorCode)ConfigService::instance().deleteADU(strADUID);
    if(ERROR_CODE_NONE_ERROR == eErrorCode && bExist)
    {
        if(MoistureController::isMoistureAdu(eType))
        {
            MoistureController::instance().removeTransmitter(strADUID);
        }

        sendManualOperatorSignal(strADUID, Monitor::ManualOpreationType::DL_OPERATION);
    }

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

/************************************************
 * 函数名:  getChannelList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道列表
 ************************************************/
void CommandService::getChannelList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);

    Others data;
    data.getChannelList(strADUID);

    sendManualOperatorSignal(strADUID, Monitor::ManualOpreationType::EDIT_OPERATION);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  getChannelInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道信息
 ************************************************/
void CommandService::getChannelInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);
    quint8 ucChannelID = request.getParameter(STR_CHANNEL_INDEX).toInt();

    ConfigData data;
    data.getChannelInfo(strADUID, ucChannelID);

    sendManualOperatorSignal(strADUID, Monitor::ManualOpreationType::EDIT_OPERATION);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  saveChannelInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存通道信息
 ************************************************/
void CommandService::saveChannelInfo(HttpRequest &request, HttpResponse &response)
{

    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);
    ADUChannelInfo channel;
    channel.etype =  ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
    channel.unID = request.getParameter(STR_CHANNEL_INDEX).toInt();
    channel.strName = request.getParameter(STR_CHANNEL_NAME);
    ADUType eADUType;
    bool  bChannelParam = false;
    switch (channel.etype)
    {
    case CHANNEL_AE://AE参数
    {
        channel.stachpara.staAEPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staAEPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staAEPara.ucSampleCycles = 50;//request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staAEPara.usSampelCount = 120;//request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
        channel.stachpara.staAEPara.fTriggerThreshold = request.getParameter(STR_PARA_TRIGGER_THRESHOLD).toFloat();
        channel.stachpara.staAEPara.sOpenTime = request.getParameter(STR_PARA_OPEN_TIME).toInt();
        channel.stachpara.staAEPara.sCloseTime = request.getParameter(STR_PARA_CLOSE_TIME).toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        channel.stachpara.staTEVPara.cBackGroundNum = request.getParameter(STR_PARA_BACK_GROUND_DATA).toInt();
    }
        break;
    case CHANNEL_UHF:
    {
        channel.stachpara.staUHFPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staUHFPara.cBandWidth = ConfigService::instance().getBandWidthEnum(request.getParameter(STR_PARA_FILTER));
        channel.stachpara.staUHFPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staUHFPara.ucSampleCycles = 50;///*ConfigService::instance().systemSettings().iFreq;//*/request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staUHFPara.usSampelCount = 120;//request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_HFCT:
    {
        channel.stachpara.staHFCTPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staHFCTPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staHFCTPara.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staHFCTPara.usSampelCount = request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_TEVPRPS:
    {
        channel.stachpara.staTEVPRPSPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
        channel.stachpara.staTEVPRPSPara.eChannelGainType = (ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL));
        channel.stachpara.staTEVPRPSPara.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staTEVPRPSPara.usSampelCount = request.getParameter(STR_PARA_SAMPLE_POINTS).toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_MECH:
    {
        channel.stachpara.staMechPara.usLoopCurrentThred = request.getParameter(STR_PARA_LOOP_CURRENT_THRED).toInt();
        channel.stachpara.staMechPara.usMotorCurrentThred = request.getParameter(STR_PARA_MOTOR_CURRENT_THRED).toInt();
        channel.stachpara.staMechPara.bSwitchState = request.getParameter(STR_PARA_SWITCH_STATE).toInt();
        channel.stachpara.staMechPara.bBreakerType = request.getParameter(STR_PARA_BREAK_TYPE).toInt();
        channel.stachpara.staMechPara.bMotorFunctionType = request.getParameter(STR_PARA_MOTOR_FUNCTION_TYPE).toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        channel.stachpara.staArresterIPara.eChannelPhase = (ChannelPhase)request.getParameter(STR_PARA_CHANNEL_PHASE).toInt();
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        channel.stachpara.staArresterUPara.eChannelPhase = (ChannelPhase)request.getParameter(STR_PARA_CHANNEL_PHASE).toInt();
        channel.stachpara.staArresterUPara.fTransformationRatio = request.getParameter(STR_PARA_TRANSFORMATION_RATIO).toFloat();
    }
        break;
    case CHANNEL_VIBRATION:
    {
        channel.stachpara.staVibrationParam.ucSampleCycles = request.getParameter(STR_PARA_SAMPLE_PERIOD).toInt();
        channel.stachpara.staVibrationParam.usSampelCount = request.getParameter(STR_PARA_SAMPLE_RATE).toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_SAW:
    {
    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staTempParam.fUpperThreshold = request.getParameter("upThreshold").toFloat();
        channel.stachpara.staTempParam.fLowerThreshold = request.getParameter("lowerThreshold").toFloat();
        channel.stachpara.staTempParam.fChangedThreshold = request.getParameter("changeThreshold").toFloat();
        channel.unID = 0;
        bChannelParam = true;
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staHumParam.fUpperThreshold = request.getParameter("upThreshold").toFloat();
        channel.stachpara.staHumParam.fLowerThreshold = request.getParameter("lowerThreshold").toFloat();
        channel.stachpara.staHumParam.fChangedThreshold = request.getParameter("changeThreshold").toFloat();
        channel.unID = 1;
        bChannelParam = true;
    }
        break;
    case CHANNEL_FLOOD:
    {
        channel.stachpara.stFloodChannelPara.cFlood = request.getParameter(STR_PARA_ALARM).toInt();
        channel.unID = 0;

    }
        break;
    case CHANNEL_SF6:
    {
        channel.stachpara.stSf6ChannelPara.fOverPressureThreshold = request.getParameter("OverPressureThreshold").toFloat();
        channel.stachpara.stSf6ChannelPara.fLowPressureThreshold = request.getParameter("LowPressureThreshold").toFloat();
        channel.stachpara.stSf6ChannelPara.fShutThreshold = request.getParameter("ShutThreshold").toFloat();
        channel.stachpara.stSf6ChannelPara.fVolumeRatio = request.getParameter("VolumeRatio").toFloat();
        bChannelParam = true;
    }
        break;
    default:
        break;
    }

    ConfigService::instance().getADUTypeFromID(strADUID, eADUType);
    if (ConfigService::instance().getISADUType(eADUType) && bChannelParam)
    {
        MonitorService::instance().setChannelInfo(strADUID, eADUType, channel.unID, channel.stachpara);
        ConfigService::instance().saveChannelName(strADUID, channel, channel.strName);
    }
    else
    {
        //待改造
        ConfigService::instance().saveChannelInfo(strADUID, channel);
        if (channel.etype == CHANNEL_AE)
        {
            channel.stachpara.staAEPara.cGain = request.getParameter(STR_PARA_GAIN).toInt();
            MonitorService::instance().setChannelInfo(strADUID, eADUType, channel.unID, channel.stachpara);
        }
        if (channel.etype == CHANNEL_TEV)
        {
            ConfigService::instance().saveChannelPara(channel.stachpara, strADUID, 1);
        }
    }

    sendManualOperatorSignal(strADUID, Monitor::ManualOpreationType::EDIT_OPERATION);

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  getRelationList
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取关联信息
 ************************************************/
void CommandService::getRelationList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    int iType = request.getParameter(STR_TYPE).toInt();
    QString strID = request.getParameter(STR_ID);
    if (iType == 1)
    {
        ConfigData data;
        data.getStation();
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
    else if (iType == 2)
    {
        Others data;
        data.getDeviceNameList();
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
    else if (iType == 3)
    {
        Others data;
        data.GetPointNameList(strID);
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
    else if (iType == 4)
    {
        Others data;
        data.getChannelListFromPoint(strID);
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
}

/************************************************
 * 函数名:  getRelation
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取关联信息
 ************************************************/
void CommandService::getRelation(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strID = request.getParameter(STR_TEST_POINT_ID);
    ConfigData data;
    data.getRelationInfo(strID);
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  saveRelation
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获保存关联信息
 ************************************************/
void CommandService::saveRelation(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    ConfigService &configService = ConfigService::instance();
    QString strPointName =  request.getParameter(STR_TEST_POINT_NAME);
    int jsonChannels = request.getParameter("aduChannelsLength").toInt();
    QList<PointConnectionInfo> listConnectionInfos;
    for (int i = 0; i < jsonChannels; i++)
    {
        PointConnectionInfo ConnectionInfo;
        ADUChannelType chaType = ConfigService::instance().getChanTypEnum(request.getParameter(QString("%1[%2][%3]").arg(STR_ADU_CHANNELS).arg(i).arg(STR_CHANNEL_TYPE).toUtf8()));
        ConnectionInfo.etype = chaType;
        ConnectionInfo.strID = request.getParameter(QString("%1[%2][%3]").arg(STR_ADU_CHANNELS).arg(i).arg(STR_ADU_ID).toUtf8());
        ConnectionInfo.unID = request.getParameter(QString("%1[%2][%3]").arg(STR_ADU_CHANNELS).arg(i).arg(STR_CHANNEL_INDEX).toUtf8()).toInt();
        listConnectionInfos.append(ConnectionInfo);
    }
    int jsonPoints = request.getParameter("testPointLength").toInt();
    for (int i = 0; i < jsonPoints; i++)
    {
        QString strPointId = request.getParameter(QString("%1[%2][%3]").arg(STR_TEST_POINT).arg(i).arg(STR_TEST_POINT_ID).toUtf8());
        configService.saveRelationInfo(strPointId, strPointName, listConnectionInfos);
    }
    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  GetLastDataID
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取最新记录id
 ************************************************/
void CommandService::GetLastDataID(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)

    HTTPErrorCode eErrorCode = ERROR_CODE_GET_DATA_NO_DATA;
    QString strPointId =  request.getParameter(STR_POINT_ID);
    ADUChannelType etype =  ConfigService::instance().getChanTypEnum(request.getParameter(STR_POINT_TYPE));
    int iDataId = DBServer::instance().lastRecordId(strPointId, etype);
    QJsonObject result;
    if (iDataId != 0)
    {
        bool bExist = false;
        QString strDeviceId;
        StationNode station = ConfigService::instance().stationNode();
        const QList<DeviceNode> &devices = station.devices;
        for(int i = 0; i < devices.size(); ++i)
        {
            const QList<TestPointInfo> &testPoints = devices[i].testPoints;
            for(int j = 0; j < testPoints.size(); ++j)
            {
                if(testPoints[j].strPointGUID == strPointId)
                {
                    bExist = true;
                    strDeviceId = devices[i].strDeviceGUID;
                    break;
                }
            }
        }

        result.insert(STR_DATA_ID, iDataId);
        result.insert(STR_DEVICE_ID, strDeviceId);
        if(!bExist)
        {
            PDS_SYS_WARNING_LOG("CommandService::GetLastDataID] testPoint not exist.");
            eErrorCode = ERROR_CODE_CONFIG_POINT_ID_NOT_EXISTED;
        }
        else
        {
            eErrorCode = ERROR_CODE_NONE_ERROR;
        }
    }
    responseWrite(QJsonDocument(encapsulationData(result,eErrorCode)).toJson());
}

/************************************************
 * 函数名:  GetMonitorTableInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取监测表信息
 ************************************************/
void CommandService::GetMonitorTableInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    int iPage = request.getParameter(STR_PAGE).toInt();
    int isize = request.getParameter(STR_SIZE).toInt();

    ConfigData data;
    data.getMonitoringFormsInfo(iPage, isize);

    responseWrite(QJsonDocument(data).toJson());
}

/************************************************
 * 函数名:  GetADUStateInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端状态信息
 ************************************************/
void CommandService::GetADUStateInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    int iPage = request.getParameter(STR_PAGE).toInt();
    int isize = request.getParameter(STR_SIZE).toInt();
    QString aduID = request.getParameter(STR_ADU_ID);
    QString isOnline = request.getParameter(STR_ADU_IS_ONLINE);

    ConfigData data;
    data.GetADUStateInfo(iPage, isize, aduID, isOnline);

    responseWrite(QJsonDocument(data).toJson());
}

/************************************************
 * 函数名:  GetADUStateInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端状态信息颜色图例
 ************************************************/
void CommandService::GetADUStateColorThred(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    int iType = request.getParameter(STR_TYPE).toInt();

    QJsonArray data;
    if ( 1 == iType )
    {
        data.append(-80);
        data.append(-100);
    }
    if ( 2 == iType )
    {
        data.append(0);
        data.append(-4);
    }
    if ( 3 == iType )
    {
        data.append(20);
        data.append(10);
    }

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  getChartData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取图谱信息
 ************************************************/
void CommandService::getChartData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)

    QString strPointId =  request.getParameter(STR_POINT_ID);
    ADUChannelType etype =  ConfigService::instance().getChanTypEnum(request.getParameter(STR_POINT_TYPE));
    int iDataType =  request.getParameter(STR_DATA_TYPE).toInt();
    int iDataId =  request.getParameter(STR_DATA_ID).toInt();

    ChartData data;
    data.getChartData(strPointId, etype, iDataType, iDataId);
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());

}

/************************************************
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  从数据库中捞数据上报
 ************************************************/
void CommandService::reportHistoryData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)

    QDateTime startDate = QDateTime::fromString(request.getParameter(STR_SRART_DATE), "yyyy-MM-dd");
    QDateTime endDate =  QDateTime::fromString(request.getParameter(STR_END_DATE), "yyyy-MM-dd");
    if ( !endDate.isValid() )
    {
        endDate = QDateTime::currentDateTime();
    }
    //OutwardService::instance().reportHistoryData(startDate, endDate);

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

void CommandService::pdsTestControl(HttpRequest &request, HttpResponse &response)
{
    int nFlag = request.getParameter("enable").toInt();
    g_bTest = (nFlag!=0);
    PDS_SYS_INFO_LOG("set test flag to %d", g_bTest);
    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  getChartData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取图谱信息
 ************************************************/
void CommandService::getChartParamData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QString strPointId =  request.getParameter(STR_POINT_ID);
    ADUChannelType etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_POINT_TYPE));
    int iDataType =  request.getParameter(STR_DATA_TYPE).toInt();
    int iDataId =  request.getParameter(STR_DATA_ID).toInt();

    ChartData data;
    data.getChartParamData(strPointId, etype, iDataType, iDataId);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());

}

/************************************************
 * 函数名:  getChartData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取图谱信息
 ************************************************/
void CommandService::GetArresterChartParamData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QString strPointId =  request.getParameter(STR_POINT_ID);
    QDateTime dateTime = QDateTime::fromString(request.getParameter(STR_DATE_TIME), STR_DATE_TIME_QSTRING_CNA);
    ChartData data;
    QJsonObject jsonData;
    data.getArresterParamData(DBServer::instance().getArresterRecord(strPointId, CHANNEL_PHASE_A, dateTime), jsonData );
    data.getArresterParamData(DBServer::instance().getArresterRecord(strPointId, CHANNEL_PHASE_B, dateTime), jsonData );
    data.getArresterParamData(DBServer::instance().getArresterRecord(strPointId, CHANNEL_PHASE_C, dateTime), jsonData );

    auto adutypes = ConfigService::instance().getAduTypeListFromPoint(strPointId);

    ADUType eADUType = ADU_TYPE_UNKNOW;
    if (adutypes.empty())
    {
        PDS_SYS_ERR_LOG("cannot get adu with pointid: %s", strPointId.toLatin1().data());
    }
    else
    {
        eADUType = adutypes.front();
    }

    if(ADU_ARRESTER_I_IS == eADUType || ADU_ARRESTER_U_IS == eADUType)
    {
        jsonData.insert(STR_SENSOR_TYPE,"Arrester");
    }
    else if(ADU_GROUNDDINGCURRENT_IS == eADUType)
    {
        jsonData.insert(STR_SENSOR_TYPE,"GrounddingCurrent");
    }
    else
    {
        jsonData.insert(STR_SENSOR_TYPE,"LeakageCurrent");
    }
    TestPointInfo stTestPoint;
    if(ConfigService::instance().getTestPoint(strPointId, stTestPoint))
    {
        jsonData.insert(STR_ADU_ID,stTestPoint.ConnectionInfo.first().strID);
        jsonData.insert(STR_POINT_NAME,stTestPoint.strOutName);
    }
    else
    {
        PDS_SYS_ERR_LOG("NO this pointid: %s", strPointId.toLatin1().data());
    }

    responseWrite(QJsonDocument(encapsulationData(jsonData)).toJson());
}


/************************************************
 * 函数名:  getTrendData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取趋势图谱信息
 ************************************************/
void CommandService::getTrendData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    QString strPointId =  request.getParameter(STR_POINT_ID);
    ADUChannelType etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_POINT_TYPE));

    QDateTime endDate;
    QDateTime startDate;

    endDate =  QDateTime::fromString(request.getParameter(STR_END_DATE), STR_DATE_TIME_QSTRING_CNA);
    startDate =  QDateTime::fromString(request.getParameter(STR_SRART_DATE), STR_DATE_TIME_QSTRING_CNA);


    if (!startDate.isValid() || !endDate.isValid())
    {
        endDate =  QDateTime::currentDateTime();
        startDate =  endDate.addDays(-7);
    }

    if (startDate.isValid() && endDate.isValid() && startDate > endDate)
    {
        responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_GET_DATA_NO_DATA)).toJson());
        return;
    }
    QString stateType = request.getParameter(STR_STATETYPE);
    int page = request.getParameter(STR_PAGE).toInt();
    int size = request.getParameter(STR_SIZE).toInt();
    ChartData data;
    if(etype == CHANNEL_FLOOD || etype == CHANNEL_FIREWORKS_ALARM)
    {
        data.getTrendData(strPointId, etype, stateType, startDate, endDate, page, size);
    }
    else if(CHANNEL_TEMPERATURE == etype)
    {
        QScopedPointer<ITrendChart> trendChart(new TempTrendChart(strPointId, startDate, endDate));
        
        responseWrite(QJsonDocument(encapsulationData(trendChart->getTrendData())).toJson());
        return;
    }
    else if(CHANNEL_HUMIDITY == etype)
    {
        QScopedPointer<ITrendChart> trendChart(new HumiTrendChart(strPointId, startDate, endDate));

        responseWrite(QJsonDocument(encapsulationData(trendChart->getTrendData())).toJson());
        return;
    }
    else if(CHANNEL_SF6 == etype)
    {
        QScopedPointer<ITrendChart> trendChart(new HumiTrendChart(strPointId, startDate, endDate));

        responseWrite(QJsonDocument(encapsulationData(trendChart->getTrendData())).toJson());
        return;
    }
    else
    {
        data.getTrendData(strPointId, etype, startDate, endDate);
    }

    if ( CHANNEL_ARRESTER_I == etype || CHANNEL_GROUNDDINGCURRENT == etype || CHANNEL_LEAKAGECURRENT == etype)
    {
        responseWrite(QJsonDocument(encapsulationData(data.value("data").toArray())).toJson());
    }
    else
    {
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }

}

void CommandService::getHistoryDatas(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QString strPointId =  request.getParameter(STR_POINT_ID);
    ADUChannelType eChannelType = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));

    QDateTime endDate =  QDateTime::fromString(request.getParameter(STR_END_DATE), STR_DATE_QSTRING_CNA);
    QDateTime startDate =  QDateTime::fromString(request.getParameter(STR_SRART_DATE), STR_DATE_QSTRING_CNA);

    int iPageCount = request.getParameter(STR_PAGE).toInt();
    int iPageSize = request.getParameter(STR_SIZE).toInt();

    ChartData chartDatas;
    chartDatas.getHistoryDatas(strPointId, eChannelType, startDate, endDate, iPageSize, iPageCount);

    responseWrite(QJsonDocument(encapsulationData(chartDatas)).toJson());

}

void CommandService::getHistoryTreeList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    int iType = request.getParameter(STR_TYPE).toInt();
    QString strID = request.getParameter(STR_ID);
    if (iType == 1)
    {
        ConfigData data;
        data.getStation();
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
    else if (iType == 2)
    {
        Others data;
        data.getDeviceNameList();
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
    else if (iType == 3)
    {
        Others data;
        data.GetPointNameList(strID);
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
    else if (iType == 4)
    {
        Others data;
        data.getHistoryTreeChannelInfoFromPoint(strID);
        responseWrite(QJsonDocument(encapsulationData(data)).toJson());
    }
}

/************************************************
 * 函数名:  getADUConfig
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端参数
 ************************************************/
void CommandService::getADUConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter( STR_ADU_TYPE ));
    ConfigData data;
    data.getADUConfig(eADUType);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  ApplyADUConfig
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  应用前端参数到同种前端
 ************************************************/
void CommandService::ApplyADUConfig(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ADUUnitInfo staADUInfo;
    ADUParam stADUParam;

    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter( STR_ADU_TYPE ));
    if (eADUType == ADU_TEMP_HUM_IS || eADUType == ADU_FLOOD_IS)
    {
        ConfigService::instance().getADUConfig(eADUType, staADUInfo);
        stADUParam = staADUInfo.stADUParam;
        stADUParam.uiAutoGivingSpace = request.getParameter(STR_ADU_AUTO_GIVISPACE).toInt();
    }

    stADUParam.ucFrequency = request.getParameter(STR_FREQUNCY).toInt();
    stADUParam.ucWorkGroup = request.getParameter(STR_ADU_WORK_GROUP).toInt();
    stADUParam.ucConnectionLoad = request.getParameter(STR_ADU_COMM_LOAD).toInt();
    stADUParam.ucConnectionSpeed = request.getParameter(STR_ADU_COMM_SPEED).toInt();
    stADUParam.uiSleepTime = request.getParameter(STR_ADU_SLEEP_TIME).toInt();
    stADUParam.uiSampleSpace = request.getParameter(STR_ADU_SAMPLE_SPACE).toInt();
    stADUParam.usStartSampleTime = request.getParameter(STR_ADU_SAMPEL_START_TIMR).toInt();
    stADUParam.ucAutoUpdate = request.getParameter(STR_ADU_B_AUTO_GIVING).toInt();
    stADUParam.eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
    stADUParam.extendInfo =  request.getParameter(STR_ADU_EXTEND);
    stADUParam.eAduFilterNoiseMode = (AduFilterNoiseMode)request.getParameter("thresholdMode").toInt();
    stADUParam.fUesdThreshold = request.getParameter("threshold").toFloat() / 100;
    if ( Monitor::WORKER_MODEL_LOWPOWER == stADUParam.eADUWorkModel )
    {
        stADUParam.ucStartArtificialTime = request.getParameter(STR_ADU_ARTIFICIAL_START_TIME).toInt();
        stADUParam.ucEndArtificialTime = request.getParameter(STR_ADU_ARTIFICIAL_END_TIME).toInt();
        stADUParam.usArtificialWakeUpInterval = request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
        stADUParam.usNotArtificialWalkUpInterval = request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
        stADUParam.bAutoChangeMode = request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE).toInt();
    }

    ConfigService &config = ConfigService::instance();
    if (config.getISADUType(eADUType))
    {
        QString strADUID;
        if(ADU_UHF_IS == eADUType)
        {
            QStringList uhfAduList = ConfigService::instance().ADUID(ADU_UHF_IS);
            bool bIsAduParamSame = true;
            foreach (QString strAduIdTmp, uhfAduList) {
                ADUUnitInfo aduInfoTmp;
                ConfigService::instance().getADU(strAduIdTmp, aduInfoTmp);
                {
                    bIsAduParamSame = (aduInfoTmp.stADUParam.eADUWorkModel == stADUParam.eADUWorkModel) &&
                            (aduInfoTmp.stADUParam.uiSleepTime == stADUParam.uiSleepTime) &&
                            (aduInfoTmp.stADUParam.ucWorkGroup == stADUParam.ucWorkGroup) &&
                            (aduInfoTmp.stADUParam.ucAutoUpdate == stADUParam.ucAutoUpdate) &&
                            (aduInfoTmp.stADUParam.ucFrequency == stADUParam.ucFrequency) &&
                            (aduInfoTmp.stADUParam.uiSampleSpace == stADUParam.uiSampleSpace) &&
                            (aduInfoTmp.stADUParam.usStartSampleTime == stADUParam.usStartSampleTime);

                    aduInfoTmp.stADUParam.eAduFilterNoiseMode = stADUParam.eAduFilterNoiseMode;
                    aduInfoTmp.stADUParam.fUesdThreshold = stADUParam.fUesdThreshold;
                    logTrace("----------------")<< strAduIdTmp << aduInfoTmp.stADUParam.eAduFilterNoiseMode << aduInfoTmp.stADUParam.fUesdThreshold;
                    ConfigService::instance().saveADUInfo(aduInfoTmp);
                }
            }

            MonitorService::instance().setADUInfo(strADUID, eADUType, stADUParam);
        }
        else
        {
            MonitorService::instance().setADUInfo(strADUID, eADUType, stADUParam);
        }
    }
    else if(MoistureController::instance().isMoistureAdu(eADUType))
    {
        QStringList adus = config.ADUID(eADUType);
        ADUUnitInfo aduinfo;
        for(int i = 0; i < adus.size(); ++i)
        {
            if(config.getADU(adus[i], aduinfo))
            {
                aduinfo.stADUParam = stADUParam;
                if(CONFIG_NO_ERROR == config.saveADUInfo(aduinfo))
                {
                    MoistureController::instance().updateTransmitterParam(adus[i], aduinfo);
                }
            }
        }
    }

    responseWrite(QJsonDocument(encapsulationData()).toJson());

}


void CommandService::getAlarmConfigInfo(HttpRequest &request, HttpResponse &response)
{


    Q_UNUSED(response);
    Q_UNUSED(request);

    int iTaskGroup =  request.getParameter(STR_SF6_ALARM_TASK_GROUP).toInt();
    ADUChannelType eChannelType = ADUChannelType(request.getParameter(STR_SF6_ALARM_CHANNEL_TYPE).toInt());

    ConfigData SF6AlarmConfigInfo;
    SF6AlarmConfigInfo.SF6AlarmConfigData(iTaskGroup, eChannelType);

    responseWrite(QJsonDocument(encapsulationData(SF6AlarmConfigInfo)).toJson());
}

/************************************************
 * 函数名:  GetAduAlarmInfo
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取传感器告警数据
 ************************************************/
void CommandService::getAduAlarmInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    const QMap<quint16, QString> qmap4AlarmType{
                {1, "FIRMWARE_EXCEPTION"},                          //固件异常
                {2, "AD_INITIALIZATION_EXCEPTION"},                //AD初始化异常
                {3, "REFERENCE_VOLTAGE_EXCEPTION"},                //参考电压异常
                {4, "ONCHIP_FLASH_EXCEPTION"},                     //片内Flash异常
                {5, "OFFCHIP_FLASH_EXCEPTION"},                    //片外Flash异常
                {6, "SYSTEM_PARAMETERS_EXCEPTION"},                //系统参数异常
                {7, "SAMPLE_PARAMETERS_EXCEPTION"},                //采集参数异常
                {8, "CALIBRATION_PARAMETERS_EXCEPTION"},           //校准参数异常
                {9, "SYSTEM_PARAMETERS_EXCEPTION_RECOVER"},        //系统参数异常恢复
                {10, "SAMPLE_PARAMETERS_EXCEPTION_RECOVER"},        //采集参数异常恢复
                {11, "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER"},   //校准参数异常恢复
                {12, "LORA_MODULE_EXCEPTION"}                      //LoRa模块异常
    };

    auto convertAlarmData = [](quint16 qui16AlarmType, QString strAlarmData)->QJsonArray
    {
        //QString strShowData = strAlarmData;
        QJsonArray alarmDataArray;

        //告警类型含义见上表
        switch (qui16AlarmType)
        {
        case 1:
        {
            if(strAlarmData == QLatin1String("1"))
            {
                //strShowData = "固件CRC计算结果和固件自带的CRC校验结果不一致";
                QJsonObject dataInfoObject;
                dataInfoObject.insert("DataType", 1000);
                dataInfoObject.insert("DataValue", "");
                alarmDataArray.append(dataInfoObject);
            }
        }
            break;
        case 2:
        {
            if(strAlarmData == QLatin1String("1"))
            {
                //strShowData = "AD初始化错误,AD无法正常工作";
                QJsonObject dataInfoObject;
                dataInfoObject.insert("DataType", 1001);
                dataInfoObject.insert("DataValue", "");
                alarmDataArray.append(dataInfoObject);
            }
        }
            break;
        case 3:
        {
            //strShowData = QString("电池电压值为%1V").arg(strAlarmData);
            QJsonObject dataInfoObject;
            dataInfoObject.insert("DataType", 1002);
            dataInfoObject.insert("DataValue", strAlarmData);
            alarmDataArray.append(dataInfoObject);
        }
            break;
        case 4:
        {
            if(strAlarmData == QLatin1String("1"))
            {
                //strShowData = "片内Flash读写错误";
                QJsonObject dataInfoObject;
                dataInfoObject.insert("DataType", 1003);
                dataInfoObject.insert("DataValue", "");
                alarmDataArray.append(dataInfoObject);
            }
        }
            break;
        case 5:
        {
            if(strAlarmData == QLatin1String("1"))
            {
                //strShowData = "片外Flash读写错误";
                QJsonObject dataInfoObject;
                dataInfoObject.insert("DataType", 1004);
                dataInfoObject.insert("DataValue", "");
                alarmDataArray.append(dataInfoObject);
            }
        }
            break;
        case 6:
        case 7:
        case 8:
        {
            bool bOk;
            uint uiData = strAlarmData.toUInt(&bOk);
            if(bOk)
            {
                //strShowData.clear();
                if(uiData & 0x01)
                {
                    //strShowData += "存储区未读取到参数;";
                    QJsonObject dataInfoObject;
                    dataInfoObject.insert("DataType", 1005);
                    dataInfoObject.insert("DataValue", "");
                    alarmDataArray.append(dataInfoObject);
                }
                if(uiData & 0x02)
                {
                    //strShowData += "读取到的参数CRC校验不一致;";
                    QJsonObject dataInfoObject;
                    dataInfoObject.insert("DataType", 1006);
                    dataInfoObject.insert("DataValue", "");
                    alarmDataArray.append(dataInfoObject);
                }
                if(uiData & 0x04)
                {
                    //strShowData += "读取到的参数超合法范围;";
                    QJsonObject dataInfoObject;
                    dataInfoObject.insert("DataType", 1007);
                    dataInfoObject.insert("DataValue", "");
                    alarmDataArray.append(dataInfoObject);
                }
            }
        }
            break;
        case 9:
        case 10:
        case 11:
        {
            if(strAlarmData == QLatin1String("0"))
            {
                //strShowData = "能够正确读取参数,且参数在合法范围内";
                QJsonObject dataInfoObject;
                dataInfoObject.insert("DataType", 1008);
                dataInfoObject.insert("DataValue", "");
                alarmDataArray.append(dataInfoObject);
            }
        }
            break;
        case 12:
        {
            if(strAlarmData == QLatin1String("1"))
            {
                //strShowData = "初始化返回值错误";
                QJsonObject dataInfoObject;
                dataInfoObject.insert("DataType", 1009);
                dataInfoObject.insert("DataValue", "");
                alarmDataArray.append(dataInfoObject);
            }
        }
            break;
        default:
            break;
        }
        //return strShowData;
        return alarmDataArray;
    };
    int page = request.getParameter(STR_PAGE).toInt();
    int size = request.getParameter(STR_SIZE).toInt();
    int getType = request.getParameter("getType").toInt();
    QString selectAduID;
    if(getType)
    {
        selectAduID = request.getParameter("aduID");
    }

    QList<AduAlarmRecord> listAduAlarmRecord, listResponData;
    DBServer::instance().getAlarmRecord(listAduAlarmRecord, selectAduID);

    QJsonObject object = encapsulationData();
    object.insert(STR_TOTAL, listAduAlarmRecord.size());
    int responseSize = page * size >= listAduAlarmRecord.size() ? listAduAlarmRecord.size() - (page - 1)* size : size;

    listResponData = listAduAlarmRecord.mid((page - 1) * size, responseSize);
    QJsonArray jsonAllAlarmRecord;
    for(int i = 0; i < responseSize; ++i)
    {
        QJsonObject jsonRecord;
        jsonRecord.insert("aduId", listResponData.at(i).strAduID);
        jsonRecord.insert("AlarmType" , qmap4AlarmType.value(listResponData.at(i).qui16AlarmType));
        jsonRecord.insert("AlarmData" , convertAlarmData(listResponData.at(i).qui16AlarmType, listResponData.at(i).strData));
        jsonRecord.insert(STR_INDEX, (page - 1)*size+i+1);
        jsonRecord.insert("AlarmTime", QDateTime::fromMSecsSinceEpoch(listResponData.at(i).qui64RecordTimeT).toString(STR_DATE_TIME_QSTRING_CNA));
        jsonAllAlarmRecord.append(jsonRecord);
    }
    object.insert(STR_ROWS, jsonAllAlarmRecord);

    responseWrite(QJsonDocument(object).toJson());
}

/************************************************
 * 函数名:  clearnADUData
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  擦除前端所有数据记录
 ************************************************/
void CommandService::clearADUData(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUId = request.getParameter(STR_ADU_ID);
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    ADUOperationType eADUOperationType = (ADUOperationType)request.getParameter(STR_ADU_OPERATION_TYPE).toInt();
    QList<QString> switchADU = ConfigService::instance().getSwitchADU();
    switch (eADUOperationType) {
    case APPLY_TO_ALL_ADU:
        for(auto i = 0; i < switchADU.size(); i++)
        {
            ADUType aduType =  ConfigService::instance().getADUTypEnum(switchADU[i]);
            if(ADU_MECH == aduType)
            {
                MechMonitor::instance().eraseAllRecords(strADUId.toInt());
            }
            else if(ADU_UHF_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_UHF_IS);
            }
            else if(ADU_HFCT_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_HFCT_IS);
            }
            else if(ADU_PD_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_PD_IS);
            }
            else if(ADU_TRANSFORMER_AE_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_TRANSFORMER_AE_IS);
            }
            else if(ADU_GIS_AE_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_GIS_AE_IS);
            }
            else if(ADU_ARRESTER_I_IS == aduType || ADU_GROUNDDINGCURRENT_IS == aduType || ADU_LEAKAGECURRENT_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_ARRESTER_I_IS);
            }
            else if(ADU_TEMP_HUM_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_TEMP_HUM_IS);
            }
            else if(ADU_TEVPRPS_IS == aduType)
            {
                MonitorService::instance().eraseAllRecords("", ADU_TEVPRPS_IS);
            }
        }
        break;
    case APPLY_TO_SAME_TYPE_ADU:
        if ( ADU_MECH == eADUType )
        {
            MechMonitor::instance().eraseAllRecords( strADUId.toInt() );
        }
        else if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            MonitorService::instance().eraseAllRecords("", eADUType);
        }
        else
        {}

        break;
    case APPLY_TO_ONE_ADU:
        if ( ( ADU_MECH == eADUType ) && (strADUId.toInt() != 0) )
        {
            MechMonitor::instance().eraseAllRecords(strADUId.toInt());
        }
        else if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            MonitorService::instance().eraseAllRecords(strADUId, eADUType);
        }
        else
        {
        }

        break;
    default:
        break;
    }

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}


/************************************************
 * 函数名:  wakeUpADU
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端日志
 ************************************************/
void CommandService::getAduLogInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    QString strADUId = request.getParameter(STR_ADU_ID);
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    bool readAll = request.getParameter(STR_ADU_LOG_READALL)[0];
    QDateTime endDate =  QDateTime::fromString(request.getParameter(STR_END_DATE), STR_DATE_QSTRING_CNA);
    QDateTime startDate =  QDateTime::fromString(request.getParameter(STR_SRART_DATE), STR_DATE_QSTRING_CNA);

    //MonitorService::instance().getADULog(strADUId, eADUType, readAll, startDate, endDate) ;

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}


/************************************************
 * 函数名:  wakeUpADU
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  唤醒前端
 ************************************************/
void CommandService::wakeUpADU(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUId = request.getParameter(STR_ADU_ID);
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

    ADUOperationType eADUOperationType = (ADUOperationType)request.getParameter(STR_ADU_OPERATION_TYPE).toInt();
    switch (eADUOperationType) {
    case APPLY_TO_ALL_ADU:
        //        MonitorService::instance().changeWorkerModel("", ADU_UHF_IS, Monitor::WORKER_MODEL_MAINTAIN);
        break;
    case APPLY_TO_SAME_TYPE_ADU:
        if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            MonitorService::instance().changeWorkerModel("", eADUType, Monitor::WORKER_MODEL_MAINTAIN);
        }
        else
        {}

        break;
    case APPLY_TO_ONE_ADU:
        if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            MonitorService::instance().changeWorkerModel(strADUId, eADUType, Monitor::WORKER_MODEL_MAINTAIN);
        }
        else
        {
        }

        break;
    default:
        break;
    }

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}


/************************************************
 * 函数名:  setLogLevel
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 设置日志等级
 * 备注： SetLogLevel?logLevel=trace
 ************************************************/
void CommandService::setLogLevel(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)

    bool bRes = true;
    QString logLevel = request.getParameter("logLevel");
    Logging::LogLevel eLevel = LogManager::instance()->logger(LOG1)->logLevel();
    if (logLevel == QString("trace"))
    {
        eLevel = Logging::TRACE;
    }
    else if (logLevel == QString("debug"))
    {
        eLevel = Logging::DEBUG;
    }
    else if (logLevel == QString("info"))
    {
        eLevel = Logging::INFO;
    }
    else if (logLevel == QString("warn"))
    {
        eLevel = Logging::WARN;
    }
    else if (logLevel == QString("error"))
    {
        eLevel = Logging::LOG_ERROR;
    }
    else if (logLevel == QString("fatal"))
    {
        eLevel = Logging::FATAL;
    }
    else if (logLevel == QString("off"))
    {
        eLevel = Logging::OFF;
    }
    else
    {
        bRes = false;
    }

    if ( bRes )
    {
        LogManager::instance()->logger(LOG1)->setLogLevel(eLevel);
        responseWrite(QJsonDocument(encapsulationData()).toJson());
    }
    else
    {
        responseWrite(QJsonDocument(encapsulationData(ERROR_COLDE_NO_PERMISSION)).toJson());
    }
}

/************************************************
 * 函数名:  getVersionInfo
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 初始化配置文件
 ************************************************/
void CommandService::getVersionInfo(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QJsonObject data;
    data.insert("version", APP_VERSION);
    data.insert("SN", monitorID());
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  connectionADU
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 连接前端
 ************************************************/
void CommandService::connectionADU(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QString strADUID;
    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    QString strPointId = request.getParameter(STR_POINT_ID);
    TestPointInfo stPoint;
    ConfigService::instance().getTestPoint(strPointId, stPoint);
    for (int i = 0; i < stPoint.ConnectionInfo.size(); i++)
    {
        ConfigService::instance().getADUTypeFromID(stPoint.ConnectionInfo.at(i).strID, eADUType);
        if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            strADUID = stPoint.ConnectionInfo.at(i).strID;
        }
    }

    quint16 resultDeley = 0; //执行结果延时(分钟)
    bool bRet = MonitorService::instance().connectionADU(strADUID, eADUType, resultDeley);
    QJsonObject jsonData;
    jsonData.insert(STR_RESULT, bRet);
    jsonData.insert(STR_ADU_ID, strADUID);

    resultDeley = resultDeley * 60 + 90;//唤醒结束耗时（延时+90秒发报文时间  单位秒）
    jsonData.insert(STR_WAIT_TIME, resultDeley);

    responseWrite(QJsonDocument(encapsulationData(jsonData)).toJson());
}

/************************************************
 * 函数名:  connectionADU
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 采集前端
 ************************************************/
void CommandService::sampleADU(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID;
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    QString strPointId = request.getParameter(STR_POINT_ID);
    TestPointInfo stPoint;
    ConfigService::instance().getTestPoint(strPointId, stPoint);
    for (int i = 0; i < stPoint.ConnectionInfo.size(); i++)
    {
        ConfigService::instance().getADUTypeFromID(stPoint.ConnectionInfo.at(i).strID, eADUType);
        if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            strADUID = stPoint.ConnectionInfo.at(i).strID;
        }
    }
    QJsonObject jsonData;
    bool bRet =  MonitorService::instance().sample(strADUID, eADUType) ;

    jsonData.insert(STR_RESULT, bRet);
    jsonData.insert(STR_ADU_ID, strADUID);

    responseWrite(QJsonDocument(encapsulationData(jsonData)).toJson());

}

/************************************************
 * 函数名:  DisconnectionADU
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 断开与前端的链接
 ************************************************/
void CommandService::disconnectionADU(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QString strADUID;
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    QString strPointId = request.getParameter(STR_POINT_ID);
    TestPointInfo stPoint;
    ConfigService::instance().getTestPoint(strPointId, stPoint);
    for (int i = 0; i < stPoint.ConnectionInfo.size(); i++)
    {
        ConfigService::instance().getADUTypeFromID(stPoint.ConnectionInfo.at(i).strID, eADUType);
        if ( ConfigService::instance().getISADUType( eADUType ) )
        {
            strADUID = stPoint.ConnectionInfo.at(i).strID;
        }
    }
    QJsonObject jsonData;

    MonitorService::instance().disconnectionADU(strADUID, eADUType);
    jsonData.insert(STR_RESULT, true);
    jsonData.insert(STR_ADU_ID, strADUID);
    responseWrite(QJsonDocument(encapsulationData(jsonData)).toJson());
}

/************************************************
 * 函数名:  initConfigFile
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 初始化配置文件
 ************************************************/
void CommandService::initConfigFile(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigService::instance().initConfig();
}

/************************************************
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 功能：获取审核数据列表
 ************************************************/
void CommandService::getCheckList(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strOpUserName = getUserName(request);
    QList<safe::CheckData> listCkData;
    safe::UserManager::instance().getCheckData(strOpUserName, 0, 0, listCkData);

    ConfigData data;
    data.getCheckList(listCkData);
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 输入参数:  request -- http请求
 *           response -- 响应
 * 功能：审核数据管理
 ************************************************/
void CommandService::checkManager(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strOpUserName = getUserName(request);
    safe::CheckData ckData;
    ckData.checkType = (safe::CheckType)request.getParameter(STR_CHECKTYPE).toInt();
    ckData.target = request.getParameter(STR_TARGET);
    ckData.sponsor = request.getParameter(STR_SPNOSOR);;
    ckData.agree = request.getParameter(STR_RESULT).toInt();
    ckData.dateTime =  QDateTime::fromString(request.getParameter(STR_DATE_TIMR), STR_DATE_TIME_QSTRING_CNA);
    safe::OpResult opResult = safe::UserManager::instance().checkData(strOpUserName, ckData);
    HTTPErrorCode errCode = ERROR_CODE_NONE_ERROR;
    errCode = getCodeFromSafe(opResult);


    safe::AOperateType aoType = safe::OPERATE_INVALID;
    if(safe::CHECK_ADDUSER == ckData.checkType)
    {
        aoType = safe:: AUDIT_ADD_USER_CHECK;
    }
    else if(safe::CHECK_DELETEUSER == ckData.checkType)
    {
        aoType = safe:: AUDIT_DELETE_USER_CHECK;
    }
    else if(safe::CHECK_CLEAREATA == ckData.checkType)
    {
        aoType = safe:: AUDIT_CLEAR_DATA_CHECK;
    }
    else if(safe::CHECK_FACTORYSET == ckData.checkType)
    {
        aoType = safe:: AUDIT_FACTORYSET_CHECK;
    }
    else
    {
        PDS_SYS_WARNING_LOG("unknow checkType %d", ckData.checkType);
    }

    if(safe::OPERATE_INVALID != aoType)
    {
        safe::AuditManager::instance().addAudit(strOpUserName, safe::AUDIT_FILE_DEVICE_SET, QDateTime::currentDateTime());
    }

    responseWrite(QJsonDocument(encapsulationData(errCode)).toJson());
}

/************************************************
 * 函数名:  saveSVGFile
 * 输入参数:  strSVGFileName -- svg文件名
 *          byteSVGData -- svg文件类容
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存svg文件
 ************************************************/
void CommandService::saveSVGFile(const QString &strSVGFileName, const QByteArray &byteSVGData)
{
    QFile fileSVG(strSVGFileName);
    fileSVG.open( QIODevice::ReadWrite );
    fileSVG.resize(0);
    fileSVG.write( QByteArray::fromBase64(byteSVGData) );
    fileSVG.flush();
    fileSVG.close();
}


/************************************************
 * 函数名:  Login
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:登录，确认用户名密码
 ************************************************/
void CommandService::Login(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QJsonObject jsonUserInfo;
    QString strName;
    QString strWord;
    safe::UserManager::instance().rasDecrypt(request.getParameter(STR_USER_NAME), strName, true);
    safe::UserManager::instance().rasDecrypt(request.getParameter(STR_PASSWORD), strWord, true);
    safe::UserInfo userInfo;
    safe::OpResult result = safe::UserManager::instance().login(strName, strWord, "", userInfo);

    if(safe::OP_RESULT_SUCCESS == result)
    {
        setCookieSession(strName, request, response);
    }

    HTTPErrorCode httpCode = getCodeFromSafe(result);
    loginResponse(userInfo, httpCode, jsonUserInfo);
    responseWrite(QJsonDocument(encapsulationData(jsonUserInfo)).toJson());
}

void CommandService::logout(HttpRequest &request, HttpResponse &response)
{
    if(!isIllegalUser(request, response))
    {
        responseWrite(QJsonDocument(encapsulationData(ERROR_COLDE_ILLEGAL_USER)).toJson());
        return;
    }

    QString strUserName;
    safe::UserManager::instance().rasDecrypt(request.getParameter(STR_USER_NAME), strUserName, true);
    logInfo("--TEST--CommandService::logout") << strUserName;
    emit sigLogout(strUserName);

    responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_NONE_ERROR)).toJson());
}


/************************************************
 * 输入参数:
 *  request -- http请求
 * 功能:  获取用户名
 ************************************************/
QString CommandService::getUserName(HttpRequest& request)
{
    QMap<QByteArray,QByteArray> mapCookies = request.getCookieMap();
    QMapIterator<QByteArray, QByteArray> i(mapCookies);
    while (i.hasNext())
    {
        QString key = i.next().key();
        if(key == STR_USER_NAME)
        {
            return i.value();
        }
    }
    return "";
}


/************************************************
 * 输入参数:
 *  request -- http请求
 *  response --http返回
 * 备注:sessionName 等于 userName 时，证明是合法用户
 * 返回值:  是否合法
 * 功能:  是否是合法用户
 ************************************************/
bool CommandService::isIllegalUser(HttpRequest& request, HttpResponse& response)
{

    QMap<QByteArray,QByteArray> mapCookies = request.getCookieMap();
    HttpSession session =  sessionStore->getSession(request, response);

    QString cookieName = "";
    QString userName = "";
    QMapIterator<QByteArray, QByteArray> i(mapCookies);
    while (i.hasNext())
    {
        QString key = i.next().key();
        if(key == "cookieRan")
        {
            cookieName = i.value();
        }
        else if(key == STR_USER_NAME)
        {
            userName = i.value();
        }
    }

    QString sessionName = "";
    if(cookieName != "")
    {
        sessionName = session.get(cookieName.toLatin1()).toString();
    }

    if((sessionName == "") || (sessionName != userName))
    {
        PDS_SYS_WARNING_LOG("warn Illegal user");
        return false;
    }
    else
    {
        return true;
    }
}

/*************************************************
输入参数：opResult 安全测评错误码
返回值：http错误码
说明：安全测评错误码转换成http错误码
*************************************************************/
HTTPErrorCode CommandService::getCodeFromSafe(const safe::OpResult opResult)
{
    HTTPErrorCode errorCode = ERROR_CODE_UNKONOW;
    switch(opResult)
    {
    case safe::OP_RESULT_SUCCESS:
        errorCode = ERROR_CODE_NONE_ERROR;
        break;
    case safe::OP_RESULT_PASS_ERROR:
        errorCode = ERROR_CODE_PASS_ERROR;
        break;
    case safe::OP_RESULT_PASSWORD_NOT_RULE:
        errorCode = ERROR_CODE_PASSWORD_NOT_RULE;
        break;
    case safe::OP_RESULT_ACCOUNT_NOT_RULE:
        errorCode = ERROR_CODE_ACCOUNT_NOT_RULE;
        break;
    case safe::OP_RESULT_ACCOUNT_NOT_EXIT:
        errorCode = ERROR_CODE_ACCOUNT_NOT_EXIT;
        break;
    case safe::OP_RESULT_IP_ILLEGAL:
        errorCode = ERROR_CODE_IP_ILLEGAL;
        break;
    default:
        PDS_SYS_ERR_LOG("unknow safe code %d", opResult);
        break;
    }
    return errorCode;
}

/************************************************
 * 函数名:  CommandService
 * 输入参数:  configFile -- 配置文件名
 *      parent -- 父Object
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  构造函数
 ************************************************/
CommandServiceForApp::CommandServiceForApp(const QString &configFile, QObject *parent)
    : HttpRequestHandler(parent)
{
    // Session store
    if(NULL == sessionStorePM)
    {
        QSettings *sessionSettings = new QSettings(configFile, QSettings::IniFormat, this);
        sessionSettings->beginGroup("sessions");
        sessionStorePM = new HttpSessionStore(sessionSettings, this);
    }

    // Static file controller
    if(NULL == staticFileControllerPM)
    {
        QSettings *fileSettings = new QSettings(configFile, QSettings::IniFormat, this);
        fileSettings->beginGroup("files");
        staticFileControllerPM = new StaticFileController(fileSettings, this);
    }

    // Configure template cache
    if(NULL == templateCachePM)
    {
        QSettings *templateSettings = new QSettings(configFile, QSettings::IniFormat, this);
        templateSettings->beginGroup("templates");
        templateCachePM = new TemplateCache(templateSettings, this);
    }
    qRegisterMetaType<QMap<QString, QString>>("QMap<QString, QString>");
    connect(this, SIGNAL(sigSampleCommand(const QString& )), this, SLOT(onSampleCommand(const QString& )));
    connect(this, SIGNAL(sigAddADUInfoApp(QMap<QString, QString>)), this, SLOT(onAddADUInfoApp(QMap<QString, QString> )));
    connect(this, SIGNAL(sigSaveADUInfoAndChannelInfoApp(QMap<QString, QString>)), this, SLOT(onSaveADUInfoAndChannelInfoApp(QMap<QString, QString> )));
    connect(this, SIGNAL(sigApplyADUConfigAndSensorConfigApp(QMap<QString, QString>)), this, SLOT(onApplyADUConfigAndSensorConfigApp(QMap<QString, QString> )));
//    connect(this, SIGNAL(sigApplyADUConfigApp(QMap<QString, QString>)), this, SLOT(onApplyADUConfigApp(QMap<QString, QString> )));
//    connect(this, SIGNAL(sigApplySensorConfigApp(QMap<QString, QString>)), this, SLOT(onApplySensorConfigApp(QMap<QString, QString> )));
//    connect(this, SIGNAL(sigSaveChannelInfoApp(QMap<QString, QString>)), this, SLOT(onSaveChannelInfoApp(QMap<QString, QString> )));

    m_appRequestRouter = QSharedPointer<AppRequestRouter>::create();
    m_pThread = new QThread;
    moveToThread(m_pThread);
    m_pThread->start();
}

/************************************************
 * 函数名:  service
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  服务函数
 ************************************************/
void CommandServiceForApp::service(HttpRequest &request, HttpResponse &response)
{
    response.setHeader("Content-Type", "text/json");
    QByteArray path = request.getPath();

    //新增请求路由，兼容以前的请求处理方式
    if(m_appRequestRouter.isNull() || !m_appRequestRouter->handleRequest(path, request, response))
    {
        if (path == "/getSingleSensorsConfig")//获取单个传感器信息
        {
            getSingleSensorsConfig(request, response);
        }
        else if (path == "/GetSysInfo")//获取系统设置
        {
            getSystemSetting(request, response);
        }
        else if(path == "/GetGroupNoRange") //获取主机区域对应的组号范围
        {
            getGroupNoRange(request, response);
        }
        else if(path == "/FindAduGroupNo") //开始扫描传感器组号接口
        {
            findAduGroupNo(request, response);
        }
        else if(path == "/CancelFindAduGroupNo") //取消传感器组号扫描
        {
            cancelFindAduGroupNo(request, response);
        }
        else if(path == "/GetFindAduGroupNoStatus") //获取扫描组号状态信息
        {
            getFindAduGroupNoStatus(request, response);
        }
        else if(path == "/GetFindAduGroupNoProgress") //获取最新组号扫描任务进度
        {
            getFindAduGroupNoProgress(request, response);
        }

        else if (path == "/updateSingleSensorsConfig")//更新单个传感器信息
        {
            updateSingleSensorsConfig(request, response);
        }
        else if (path == "/getAllSensorsConfig")//获取所有传感器的运维信息
        {
            getAllSensorsConfig(request, response);
        }
        else if (path == "/updateAllSensorsConfig")//更新所有传感器的运维信息
        {
            updateAllSensorsConfig(request, response);
        }
        else if(path == "/getSensorSignalTrend")//获取传感器的历史信号强度和信噪比
        {
            getSensorSignalTrend(request, response);
        }
        else if (path == "/ping")//测试链路
        {
            QJsonObject result;
            HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

            result.insert("result", "pong");

            responseWrite(QJsonDocument(encapsulationData(result,eErrorCode)).toJson());
        }
        else if (path == "/GetCommonType")//获取通用类型枚举接口
        {
            GetCommonTypeApp(request, response);
        }
        else if (path == "/GetStation")//获取站点信息
        {
            GetStationApp(request, response);
        }
        else if (path == "/SaveStationInfo")//保存站点信息
        {
            SaveStationInfoApp(request, response);
        }
        else if (path == "/SaveDeviceInfo")//保存/修改一次设备信息
        {
            SaveDeviceInfoApp(request, response);
        }
        else if (path == "/DeleteDeviceInfo")//删除一次设备
        {
            DeleteDeviceInfoApp(request, response);
        }
        else if (path == "/SavePointInfo")//保存测点信息
        {
            SavePointInfoApp(request, response);
        }
        else if (path == "/DeletePointInfo")//删除测点信息
        {
            DeletePointInfoApp(request, response);
        }
        else if (path == "/GetADUVersionList")//获取前端版本信息
        {
            GetADUVersionListApp(request, response);
        }
        else if (path == "/GetChannelList")//获取前端下通道列表
        {
            getChannelListApp(request, response);
        }
        else if (path == "/SaveRelation")//保存关联信息
        {
            saveRelationApp(request, response);
        }
        else if (path == "/GetDeviceNameList")//获取一次设备名称列表
        {
            GetDeviceNameListApp(request, response);
        }
        else if (path == "/GetPointNameList")//获取测点列表
        {
            GetPointNameListApp(request, response);
        }
        else if (path == "/SaveADUInfo")//保存前端信息
        {
            saveADUInfoApp(request, response);
        }
        else if (path == "/AddADUInfo")//新增前端
        {
            addADUInfoApp(request, response);
        }
        else if (path == "/DeleteADUInfo")//删除前端信息
        {
            deleteADUInfoApp(request, response);
        }
        else if (path == "/GetADUList")//获取前端列表
        {
            GetADUListApp(request, response);
        }
        else if (path == "/GetADUInfo")//获取前端信息
        {
            GetADUInfoApp(request, response);
        }
        else if (path == "/GetChannelInfo")//获取通道信息
        {
            GetChannelInfoApp(request, response);
        }
        else if (path == "/Get104InfoAddr")//获取104点表信息
        {
            Get104InfoAddrApp(request, response);
        }
        else if (path == "/ApplyADUConfig")//应用到同种类型前端
        {
            ApplyADUConfigApp(request, response);
        }
        else if (path == "/GetLoraFrequency")//获取主机的lora频点
        {
            QJsonObject result;
            HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
            int type = BAND_NUM;
            type = NetworkDeviceManager::instance().getLoraDevice()->getLoraBandType();
            QString LoraBandType;
            switch (type) {
            case BAND_CN470:
                LoraBandType = "BAND_CN470";
                break;
            case BAND_CN779:
                LoraBandType = "BAND_CN779";
                break;
            case BAND_AS920:
                LoraBandType = "BAND_AS920";
                break;
            case BAND_AS923:
                LoraBandType = "BAND_AS923";
                break;
            case BAND_US902:
                LoraBandType = "BAND_US902";
                break;
            case BAND_EU863:
                LoraBandType = "BAND_EU863";
                break;
            case BAND_EU433:
                LoraBandType = "BAND_EU433";
                break;
            case BAND_AU915:
                LoraBandType = "BAND_AU915";
                break;
            case BAND_KR920:
                LoraBandType = "BAND_KR920";
                break;
            case BAND_IN865:
                LoraBandType = "BAND_IN865";
                break;
            default:
                LoraBandType = "读取错误";
                break;
            }

            result.insert("result", LoraBandType);

            responseWrite(QJsonDocument(encapsulationData(result,eErrorCode)).toJson());
        }
        else if (path == "/GetSyncDataADUType")//获取同步数据前端类型
        {
            getSyncDataADUType(request, response);
        }
        else if (path == "/syncAduData") //同步传感器数据
        {
            syncAduDataForApp(request, response);
        }
        else if (path == "/cancelSyncAduData") //取消同步传感器数据
        {
            cancelSyncAduDataForApp(request, response);
        }
        else if (path == "/getAduSyncDataStatus") //获取同步传感器数据状态
        {
            getAduSyncDataStatusForApp(request, response);
        }
        else if (path == "/uploadAduFirmware") //上传传感器固件
        {
            uploadAduFirmware(request, response);
        }
        else if (path == "/getUpgradeAduInfo")  //获取升级传感器信息
        {
            getUpgradeAduInfo(request, response);
        }
        else if (path == "/upgradeAduFirmware") //升级传感器固件
        {
            upgradeAduFirmware(request, response);
        }
        else if (path == "/firmwareUpgradeTaskList") //获取固件升级任务列表
        {
            getUpgradeAduTaskRecord(request, response);
        }
        else if (path == "/upgradeTaskRecord")   //获取固件升级任务详情
        {
            getUpgradeAduTaskDetails(request, response);
        }
        else if (path == "/getUpgradeAduTaskLog")   //获取升级传感器升级日志
        {
            getUpgradeAduTaskLog(request, response);
        }
        else if (path == "/getUpgradeTaskStatus")   //获取升级任务状态
        {
            getUpgradeTaskStatus(request, response);
        }
        else if (path == "/getAduFirmwareFileStatus")   //获取传感器固件文件状态
        {
            getAduFirmwareFileStatus(request, response);
        }
        else
        {
            QJsonObject result;
            HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

            result.insert("result", "链接正常，请输入正确url");

            responseWrite(QJsonDocument(encapsulationData(result,eErrorCode)).toJson());
        }
    }
}

void CommandServiceForApp::getSystemSetting(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)
    ConfigData data;
    data.SystemSettingData();
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

void CommandServiceForApp::getGroupNoRange(HttpRequest &request, HttpResponse &response)
{
    const int iLorafrequency = request.getParameter(STR_MONITOR_LORA_FREQUENCEY).toInt();
    if(iLorafrequency < 0 || iLorafrequency > (int)AREA_CHINA_RSV)
    {
        HTTPErrorCode eErrorCode = ERROR_CODE_PARAMETER_ERROR;
        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
        return;
    }
    ConfigData data;
    data.getGroupNoRange(iLorafrequency);
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

void CommandServiceForApp::findAduGroupNo(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    const ADUType eAduType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    const QStringList strFindAduList = QString(request.getParameter(STR_ADU_LIST)).split(",", QString::SkipEmptyParts);
    const int iStartGroupNo = QString(request.getParameter("startGroupNo")).toInt();
    const int iEndGroupNo = QString(request.getParameter("endGroupNo")).toInt();

    if(ADU_TYPE_UNKNOW == eAduType || strFindAduList.isEmpty() || iEndGroupNo < 0 || iStartGroupNo < 0 || iEndGroupNo < iStartGroupNo)
    {
        eErrorCode = ERROR_CODE_PARAMETER_ERROR;
    }
    else
    {
        logInfo("CommandService::findAduGroupNo") << eAduType << strFindAduList << iStartGroupNo << iEndGroupNo;

        if(!MonitorService::instance().findAduGroupNo(eAduType, strFindAduList, iStartGroupNo, iEndGroupNo))
        {
            eErrorCode = ERROR_CODE_FIND_ADU_GROUPNO_ERROR;
        }
    }

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

void CommandServiceForApp::cancelFindAduGroupNo(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    const ADUType eAduType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

    logInfo("CommandService::cancelFindAduGroupNo") << eAduType;


    MonitorService::instance().cancelFindAduGroupNo(eAduType);

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

void CommandServiceForApp::getFindAduGroupNoStatus(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(request)
    logInfo("CommandService::getFindAduGroupNoStatus");
    QJsonObject result;

    MonitorService::instance().getFindAduGroupNoStatus(result);

    responseWrite(QJsonDocument(encapsulationData(result)).toJson());
}

void CommandServiceForApp::getFindAduGroupNoProgress(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(request)
    logInfo("CommandService::getFindAduGroupNoProgress");
    QJsonObject result;

    MonitorService::instance().getFindAduGroupNoProgress(result);

    responseWrite(QJsonDocument(encapsulationData(result)).toJson());
}

/************************************************
 * 函数名:  ApplySensorConfigApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  将该传感器参数应用于同类传感器
 ************************************************/
void CommandServiceForApp::ApplySensorConfigApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    QMap<QString, QString> items;

    ADUChannelType etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
    items.insert("etype",QString("%1").arg(etype));
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    items.insert("eADUType",QString("%1").arg(eADUType));
    PDS_SYS_INFO_LOG("Begin to set sensor config, channel type : %d, adu type : %d", etype, eADUType);
    switch (etype)
    {
    case CHANNEL_AE://AE参数
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg(ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
        items.insert("usSampelCount",request.getParameter(STR_PARA_SAMPLE_POINTS));
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        items.insert("cBackGroundNum",request.getParameter(STR_PARA_BACK_GROUND_DATA));
    }
        break;
    case CHANNEL_UHF:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("cBandWidth",QString("%1").arg(ConfigService::instance().getBandWidthEnum(request.getParameter(STR_PARA_FILTER))));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
    }
        break;
    case CHANNEL_HFCT:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
    }
        break;
    case CHANNEL_MECH:
    {
        items.insert("usLoopCurrentThred",request.getParameter(STR_PARA_LOOP_CURRENT_THRED));
        items.insert("usMotorCurrentThred",request.getParameter(STR_PARA_MOTOR_CURRENT_THRED));
        items.insert("bSwitchState",request.getParameter(STR_PARA_SWITCH_STATE));
        items.insert("bBreakerType",request.getParameter(STR_PARA_BREAK_TYPE));
        items.insert("bMotorFunctionType",request.getParameter(STR_PARA_MOTOR_FUNCTION_TYPE));
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
        items.insert("fTransformationRatio",request.getParameter(STR_PARA_TRANSFORMATION_RATIO));
    }
        break;
    case CHANNEL_VIBRATION:
    {
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
        items.insert("usSampelCount",request.getParameter(STR_PARA_SAMPLE_RATE));
    }
        break;
    case CHANNEL_SAW:
    {

    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));
    }
        break;
    case CHANNEL_FLOOD:
    {
        items.insert("cFlood",request.getParameter(STR_PARA_ALARM));
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));
    }
        break;
    default:
    {
        PDS_SYS_WARNING_LOG("Unsupport channel type %d", etype);
        break;
    }
    }
    if (eADUType == ADU_PD_THREE || eADUType == ADU_PD_FIVE)
    {
        QString strChannelName = QString(request.getParameter(STR_CHANNEL_TYPE));
        items.insert("strChannelName",strChannelName);
        if(strChannelName == "TEV")
        {
            ADUChannelType etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
            items.insert("etype",QString("%1").arg(etype));
            items.insert("unID",request.getParameter(STR_CHANNEL_INDEX));
            items.insert("strName",request.getParameter(STR_CHANNEL_TYPE));
            items.insert("cBackGroundNum",request.getParameter(STR_PARA_BACK_GROUND_DATA));
        }
        if(strChannelName == "AE")
        {
            items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        }
    }

    emit sigApplySensorConfigApp(items);

    responseWrite(QJsonDocument(encapsulationData()).toJson());

}

/************************************************
 * 函数名:  ApplyADUConfigApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  应用前端参数到同种前端
 ************************************************/
//void CommandServiceForApp::ApplyADUConfigApp(HttpRequest &request, HttpResponse &response)
//{
//    QMap<QString, QString> items;

//    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter( STR_ADU_TYPE ));
//    if (eADUType == ADU_TEMP_HUM_IS || eADUType == ADU_FLOOD_IS)
//    {
//        items.insert("uiAutoGivingSpace", request.getParameter(STR_ADU_AUTO_GIVISPACE));
//    }
//    items.insert("eType",QString("%1").arg(eADUType));
//    items.insert("ucFrequency",request.getParameter(STR_FREQUNCY));
//    items.insert("ucWorkGroup",request.getParameter(STR_ADU_WORK_GROUP));
//    items.insert("ucConnectionLoad",request.getParameter(STR_ADU_COMM_LOAD));
//    items.insert("ucConnectionSpeed",request.getParameter(STR_ADU_COMM_SPEED));
//    items.insert("uiSleepTime",request.getParameter(STR_ADU_SLEEP_TIME));
//    items.insert("uiSampleSpace",request.getParameter(STR_ADU_SAMPLE_SPACE));
//    items.insert("usStartSampleTime",request.getParameter(STR_ADU_SAMPEL_START_TIMR));
//    items.insert("ucAutoUpdate",request.getParameter(STR_ADU_B_AUTO_GIVING));
//    Monitor::ADUWorkMode eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
//    items.insert("eADUWorkModel",QString("%1").arg(eADUWorkModel));
//    items.insert("extendInfo",request.getParameter(STR_ADU_EXTEND));
//    if ( Monitor::WORKER_MODEL_LOWPOWER == eADUWorkModel )
//    {
//        items.insert("ucStartArtificialTime",request.getParameter(STR_ADU_ARTIFICIAL_START_TIME));
//        items.insert("ucEndArtificialTime",request.getParameter(STR_ADU_ARTIFICIAL_END_TIME));
//        items.insert("usArtificialWakeUpInterval",request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE));
//        items.insert("usNotArtificialWalkUpInterval",request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE));
//        items.insert("bAutoChangeMode",request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE));
//    }

//    emit sigApplyADUConfigApp(items);

//    responseWrite(QJsonDocument(encapsulationData()).toJson());

//}

/************************************************
 * 函数名:  ApplyADUConfigApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  应用前端参数到同种前端
 ************************************************/
void CommandServiceForApp::ApplyADUConfigApp(HttpRequest &request, HttpResponse &response)
{
    QMap<QString, QString> items;

    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter( STR_ADU_TYPE ));
    if (eADUType == ADU_TEMP_HUM_IS || eADUType == ADU_FLOOD_IS)
    {
        items.insert("uiAutoGivingSpace", request.getParameter(STR_ADU_AUTO_GIVISPACE));
    }
    ADUChannelType etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
    items.insert("eChannelType",QString("%1").arg(etype));
    switch (etype)
    {
    case CHANNEL_AE://AE参数
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg(ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
        items.insert("usSampelCount",request.getParameter(STR_PARA_SAMPLE_POINTS));
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        items.insert("cBackGroundNum",request.getParameter(STR_PARA_BACK_GROUND_DATA));
    }
        break;
    case CHANNEL_UHF:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("cBandWidth",QString("%1").arg(ConfigService::instance().getBandWidthEnum(request.getParameter(STR_PARA_FILTER))));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
    }
        break;
    case CHANNEL_HFCT:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
    }
        break;
    case CHANNEL_MECH:
    {
        items.insert("usLoopCurrentThred",request.getParameter(STR_PARA_LOOP_CURRENT_THRED));
        items.insert("usMotorCurrentThred",request.getParameter(STR_PARA_MOTOR_CURRENT_THRED));
        items.insert("bSwitchState",request.getParameter(STR_PARA_SWITCH_STATE));
        items.insert("bBreakerType",request.getParameter(STR_PARA_BREAK_TYPE));
        items.insert("bMotorFunctionType",request.getParameter(STR_PARA_MOTOR_FUNCTION_TYPE));
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
        items.insert("fTransformationRatio",request.getParameter(STR_PARA_TRANSFORMATION_RATIO));
    }
        break;
    case CHANNEL_VIBRATION:
    {
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
        items.insert("usSampelCount",request.getParameter(STR_PARA_SAMPLE_RATE));
    }
        break;
    case CHANNEL_SAW:
    {

    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));
    }
        break;
    case CHANNEL_FLOOD:
    {
        items.insert("cFlood",request.getParameter(STR_PARA_ALARM));
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));
    }
        break;
    default:
    {
        PDS_SYS_WARNING_LOG("Unsupport channel type %d", etype);
        break;
    }
    }
    if (eADUType == ADU_PD_THREE || eADUType == ADU_PD_FIVE)
    {
        QString strChannelName = QString(request.getParameter(STR_CHANNEL_TYPE));
        items.insert("strChannelName",strChannelName);
        if(strChannelName == "TEV")
        {
            ADUChannelType etype = ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
            items.insert("eChannelType",QString("%1").arg(etype));
            items.insert("unID",request.getParameter(STR_CHANNEL_INDEX));
            items.insert("strName",request.getParameter(STR_CHANNEL_TYPE));
            items.insert("cBackGroundNum",request.getParameter(STR_PARA_BACK_GROUND_DATA));
        }
        if(strChannelName == "AE")
        {
            items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        }
    }

    items.insert("eType",QString("%1").arg(eADUType));
    items.insert("ucFrequency",request.getParameter(STR_FREQUNCY));
    items.insert("ucWorkGroup",request.getParameter(STR_ADU_WORK_GROUP));
    items.insert("ucConnectionLoad",request.getParameter(STR_ADU_COMM_LOAD));
    items.insert("ucConnectionSpeed",request.getParameter(STR_ADU_COMM_SPEED));
    items.insert("uiSleepTime",request.getParameter(STR_ADU_SLEEP_TIME));
    items.insert("uiSampleSpace",request.getParameter(STR_ADU_SAMPLE_SPACE));
    items.insert("usStartSampleTime",request.getParameter(STR_ADU_SAMPEL_START_TIMR));
    items.insert("ucAutoUpdate",request.getParameter(STR_ADU_B_AUTO_GIVING));
    Monitor::ADUWorkMode eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
    items.insert("eADUWorkModel",QString("%1").arg(eADUWorkModel));
    items.insert("extendInfo",request.getParameter(STR_ADU_EXTEND));
    if ( Monitor::WORKER_MODEL_LOWPOWER == eADUWorkModel )
    {
        items.insert("ucStartArtificialTime",request.getParameter(STR_ADU_ARTIFICIAL_START_TIME));
        items.insert("ucEndArtificialTime",request.getParameter(STR_ADU_ARTIFICIAL_END_TIME));
        items.insert("usArtificialWakeUpInterval",request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE));
        items.insert("usNotArtificialWalkUpInterval",request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE));
        items.insert("bAutoChangeMode",request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE));
    }

    emit sigApplyADUConfigAndSensorConfigApp(items);

    responseWrite(QJsonDocument(encapsulationData()).toJson());

}


/************************************************
 * 函数名:  GetChannelInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道信息
 ************************************************/
void CommandServiceForApp::GetChannelInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);
    quint8 ucChannelID = request.getParameter(STR_CHANNEL_INDEX).toInt();

    ConfigData data;
    data.getChannelInfo(strADUID, ucChannelID);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  Get104InfoAddrApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道信息
 ************************************************/
void CommandServiceForApp::Get104InfoAddrApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    Others data;

    int addr= 0;
    int startAddr = 0x4001;
    StationNode stStationNode =  ConfigService::instance().stationNode();

    foreach( DeviceNode stDevice, stStationNode.devices )
    {
        foreach ( TestPointInfo stPoint, stDevice.testPoints )
        {
            //根据测点类型分配信息地址，信息地址范围暂定4001H~5000H
            int testPointAddress = startAddr + addr;
            QString testPointName    = stPoint.strOutName;

            foreach ( PointConnectionInfo stPointConnectionInfo, stPoint.ConnectionInfo)
            {
                switch (stPointConnectionInfo.etype)
                {
                    case CHANNEL_AE:
                        {
                        addr = addr + 5;

                        QJsonObject infoAddr1;
                        infoAddr1.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr1.insert("104addr",testPointAddress);
                        infoAddr1.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr1.insert("telemetryName",(testPointName+"|局放告警").toUtf8().data());
                        data.append(infoAddr1);

                        QJsonObject infoAddr2;
                        infoAddr2.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr2.insert("104addr",testPointAddress+1);
                        infoAddr2.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr2.insert("telemetryName",(testPointName+"|有效值").toUtf8().data());
                        data.append(infoAddr2);

                        QJsonObject infoAddr3;
                        infoAddr3.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr3.insert("104addr",testPointAddress+2);
                        infoAddr3.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr3.insert("telemetryName",(testPointName+"|周期最大值").toUtf8().data());
                        data.append(infoAddr3);

                        QJsonObject infoAddr4;
                        infoAddr4.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr4.insert("104addr",testPointAddress+3);
                        infoAddr4.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr4.insert("telemetryName",(testPointName+"|频率分量1").toUtf8().data());
                        data.append(infoAddr4);

                        QJsonObject infoAddr5;
                        infoAddr5.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr5.insert("104addr",testPointAddress+4);
                        infoAddr5.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr5.insert("telemetryName",(testPointName+"|频率分量2").toUtf8().data());
                        data.append(infoAddr5);
                        }
                        break;
                    case CHANNEL_TEV:
                        {
                        addr = addr + 2;

                        QJsonObject infoAddr1;
                        infoAddr1.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr1.insert("104addr",testPointAddress);
                        infoAddr1.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr1.insert("telemetryName",(testPointName+"|局放告警").toUtf8().data());
                        data.append(infoAddr1);

                        QJsonObject infoAddr2;
                        infoAddr2.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr2.insert("104addr",testPointAddress+1);
                        infoAddr2.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr2.insert("telemetryName",(testPointName+"|地电波等级").toUtf8().data());
                        data.append(infoAddr2);
                        }
                        break;
                    case CHANNEL_TEMPERATURE:
                        {
                        addr++;
                        QJsonObject infoAddr1;
                        infoAddr1.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr1.insert("104addr",testPointAddress);
                        infoAddr1.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr1.insert("telemetryName",(testPointName+"|温度").toUtf8().data());
                        data.append(infoAddr1);
                        }
                        break;
                    case CHANNEL_MECH:
                        {
                        addr++;
                        QJsonObject infoAddr1;
                        infoAddr1.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr1.insert("104addr",testPointAddress);
                        infoAddr1.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr1.insert("telemetryName",(testPointName+"|机械特性").toUtf8().data());
                        data.append(infoAddr1);
                        }
                        break;
                    case CHANNEL_UHF:
                        {
                        addr = addr + 7;

                        QJsonObject infoAddr1;
                        infoAddr1.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr1.insert("104addr",testPointAddress);
                        infoAddr1.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr1.insert("telemetryName",(testPointName+"|局放告警").toUtf8().data());
                        data.append(infoAddr1);

                        QJsonObject infoAddr2;
                        infoAddr2.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr2.insert("104addr",testPointAddress+1);
                        infoAddr2.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr2.insert("telemetryName",(testPointName+"|周期数").toUtf8().data());
                        data.append(infoAddr2);

                        QJsonObject infoAddr3;
                        infoAddr3.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr3.insert("104addr",testPointAddress+2);
                        infoAddr3.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr3.insert("telemetryName",(testPointName+"|相位数").toUtf8().data());
                        data.append(infoAddr3);

                        QJsonObject infoAddr4;
                        infoAddr4.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr4.insert("104addr",testPointAddress+3);
                        infoAddr4.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr4.insert("telemetryName",(testPointName+"|平均放电幅值").toUtf8().data());
                        data.append(infoAddr4);

                        QJsonObject infoAddr5;
                        infoAddr5.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr5.insert("104addr",testPointAddress+4);
                        infoAddr5.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr5.insert("telemetryName",(testPointName+"|最大放电幅值").toUtf8().data());
                        data.append(infoAddr5);

                        QJsonObject infoAddr6;
                        infoAddr6.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr6.insert("104addr",testPointAddress+5);
                        infoAddr6.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr6.insert("telemetryName",(testPointName+"|放电次数").toUtf8().data());
                        data.append(infoAddr6);

                        QJsonObject infoAddr7;
                        infoAddr7.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr7.insert("104addr",testPointAddress+6);
                        infoAddr7.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr7.insert("telemetryName",(testPointName+"|局放类型").toUtf8().data());
                        data.append(infoAddr7);
                        }
                        break;
                    case CHANNEL_HFCT:
                        {
                        addr = addr + 5;

                        QJsonObject infoAddr1;
                        infoAddr1.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr1.insert("104addr",testPointAddress);
                        infoAddr1.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr1.insert("telemetryName",(testPointName+"|局放告警").toUtf8().data());
                        data.append(infoAddr1);

                        QJsonObject infoAddr2;
                        infoAddr2.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr2.insert("104addr",testPointAddress+1);
                        infoAddr2.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr2.insert("telemetryName",(testPointName+"|工频周期").toUtf8().data());
                        data.append(infoAddr2);

                        QJsonObject infoAddr3;
                        infoAddr3.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr3.insert("104addr",testPointAddress+2);
                        infoAddr3.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr3.insert("telemetryName",(testPointName+"|平均放电幅值").toUtf8().data());
                        data.append(infoAddr3);

                        QJsonObject infoAddr4;
                        infoAddr4.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr4.insert("104addr",testPointAddress+3);
                        infoAddr4.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr4.insert("telemetryName",(testPointName+"|最大放电幅值").toUtf8().data());
                        data.append(infoAddr4);

                        QJsonObject infoAddr5;
                        infoAddr5.insert("AduID",stPointConnectionInfo.strID);
                        infoAddr5.insert("104addr",testPointAddress+4);
                        infoAddr5.insert("deviceName",stDevice.strName.toUtf8().data());
                        infoAddr5.insert("telemetryName",(testPointName+"|脉冲个数").toUtf8().data());
                        data.append(infoAddr5);
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  GetADUInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端详情
 ************************************************/
void CommandServiceForApp::GetADUInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);

    ConfigData data;
    ADUUnitInfo adu;
    if (ConfigService::instance().getADU(strADUID,adu))
    {
        data.insert(STR_ADU_ID,adu.strID);
        data.insert(STR_ADU_NAME,adu.strName);
        QString strName = ConfigService::instance().getADUTypName(adu.eType);
        data.insert(STR_ADU_TYPE, strName);
        data.insert(STR_ADU_COMM_TYPE, ConfigService::instance().mapLinkGrp().key(adu.eLinkGroup));
        data.insert(STR_FREQUNCY,adu.stADUParam.ucFrequency);
        data.insert(STR_ADU_WORK_GROUP,adu.stADUParam.ucWorkGroup);
        data.insert(STR_ADU_NUM_IN_GROUP,adu.stADUParam.usNumInGroup);
        data.insert(STR_ADU_COMM_LOAD,adu.stADUParam.ucConnectionLoad);
        data.insert(STR_ADU_COMM_SPEED,adu.stADUParam.ucConnectionSpeed);
        data.insert(STR_ADU_SLEEP_TIME,(int)adu.stADUParam.uiSleepTime);
        data.insert(STR_ADU_SAMPLE_SPACE,(int)adu.stADUParam.uiSampleSpace);
        data.insert(STR_ADU_SAMPEL_START_TIMR,adu.stADUParam.usStartSampleTime);
        data.insert(STR_ADU_ARTIFICIAL_START_TIME,8);//adu.stADUParam.ucStartArtificialTime);
        data.insert(STR_ADU_ARTIFICIAL_END_TIME,20);//adu.stADUParam.ucEndArtificialTime);
        data.insert(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE,3);//adu.stADUParam.usArtificialWakeUpInterval);
        data.insert(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE,30);//adu.stADUParam.usNotArtificialWalkUpInterval);
        data.insert(STR_ADU_IS_AUTO_CHANGE_MODE, 1);
        //        if ( Monitor::WORKER_MODEL_NORMAL != adu.stADUParam.eADUWorkModel)
        //        {
        data.insert(STR_ADU_MODE, ConfigService::instance().getADUWorkMode(adu.stADUParam.eADUWorkModel));
        //        }
        data.insert(STR_ADU_B_AUTO_GIVING,  adu.stADUParam.ucAutoUpdate);
        data.insert(STR_ADU_AUTO_GIVISPACE, (int)adu.stADUParam.uiAutoGivingSpace);
        data.insert(STR_ADU_MONITOR_MODE_SAMPLE_SPACE,adu.stADUParam.usTutelageSampleSpace);
        data.insert(STR_ADU_RS485_COM_PORT, adu.strRS485ComPort);
        data.insert(STR_SF6_ALARM_TASK_GROUP, (int)adu.iTaskGroup);
        data.insert(STR_ADU_EXTEND, adu.stADUParam.extendInfo);

        QJsonArray jsonchannelList;
        for (int i = 0; i < adu.Channels.size(); i++)
        {
            QJsonObject jsonChannel;
            QString chanName = ConfigService::instance().getChanTypName(adu.Channels.at(i).etype);
            jsonChannel.insert(STR_CHANNEL_TYPE, chanName);
            jsonChannel.insert(STR_CHANNEL_INDEX, adu.Channels.at(i).unID);
            jsonChannel.insert(STR_CHANNEL_NAME, adu.Channels.at(i).strName);
            jsonChannel.insert(STR_ID, adu.Channels.at(i).unID);
            jsonChannel.insert(STR_NAME, adu.Channels.at(i).strName);
            jsonChannel.insert(STR_TYPE, 4);

            ADUChannelInfo channel;
            if (ConfigService::instance().getChannelInfo(strADUID,adu.Channels.at(i).unID,channel))
            {
                switch (channel.etype)
                {
                case CHANNEL_AE://AE参数
                {
                    AEChannelPara stAEChannelPara =  channel.stachpara.staAEPara;
                    jsonChannel.insert(STR_PARA_GAIN,stAEChannelPara.cGain);//增益
                    QJsonArray jsonGains;
                    jsonGains.append(60);
                    jsonGains.append(80);
                    jsonGains.append(100);
                    jsonChannel.insert(STR_PARA_GAIN_LIST, jsonGains);
                    jsonChannel.insert(STR_PARA_GAIN_MODEL, ConfigService::instance().listGainModel().at(stAEChannelPara.eChannelGainType));//增益类型
                    QJsonArray jsonGainModels;
                    QList<QString> gainModels = ConfigService::instance().listGainModel();
                    for (int i = 0; i < gainModels.size(); i++)
                    {
                        jsonGainModels.append(gainModels.at(i));
                    }
                    jsonChannel.insert(STR_PARA_GAIN_MODEL_LIST, jsonGainModels);
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD,stAEChannelPara.ucSampleCycles);//采样周期数
                    QJsonArray jsonSamplePeriods;
                    jsonSamplePeriods.append(2);
                    jsonSamplePeriods.append(5);
                    jsonSamplePeriods.append(10);
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
                    jsonChannel.insert(STR_PARA_SAMPLE_POINTS,stAEChannelPara.usSampelCount);//采样点数
                }
                    break;
                case CHANNEL_TEV://TEV参数
                {
                    jsonChannel.insert(STR_PARA_BACK_GROUND_DATA, channel.stachpara.staTEVPara.cBackGroundNum);
                }
                    break;
                case CHANNEL_UHF:
                {
                    UHFChannelPara stUHFChannelPara =  channel.stachpara.staUHFPara;
                    jsonChannel.insert(STR_PARA_GAIN,stUHFChannelPara.cGain);//增益
                    QJsonArray jsonGains;
                    jsonGains.append(0);
                    jsonGains.append(20);
                    jsonChannel.insert(STR_PARA_GAIN_LIST, jsonGains);
                    jsonChannel.insert(STR_PARA_GAIN_MODEL, ConfigService::instance().listGainModel().at(stUHFChannelPara.eChannelGainType));//增益类型
                    QJsonArray jsonGainModels;
                    QList<QString> gainModels = ConfigService::instance().listGainModel();
                    for (int i = 0; i < gainModels.size(); i++)
                    {
                        jsonGainModels.append(gainModels.at(i));
                    }
                    jsonChannel.insert(STR_PARA_GAIN_MODEL_LIST, jsonGainModels);
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD,stUHFChannelPara.ucSampleCycles);//采样周期数
                    QJsonArray jsonSamplePeriods;
                    jsonSamplePeriods.append(50);
                    jsonSamplePeriods.append(60);
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
                    jsonChannel.insert(STR_PARA_SAMPLE_POINTS,stUHFChannelPara.usSampelCount);//采样点数
                    QJsonArray jsonSamplePonits;
                    jsonSamplePonits.append(60);
                    jsonSamplePonits.append(72);
                    jsonChannel.insert(STR_PARA_SAMPLE_POINTS_LIST, jsonSamplePonits);
                    jsonChannel.insert(STR_PARA_FILTER, ConfigService::instance().getBandWidthName(stUHFChannelPara.cBandWidth));//带宽
                    QJsonArray jsonBandWidths;
                    QList<QString> bandWidths = ConfigService::instance().listBandWidth();
                    for (int i = 0; i < bandWidths.size(); i++)
                    {
                        jsonBandWidths.append(bandWidths.at(i));
                    }
                    jsonChannel.insert(STR_PARA_BAND_WIDTH_LIST, jsonBandWidths);
                }
                    break;
                case CHANNEL_HFCT:
                {
                    HFCTChannelPara stHFCTChannelPara =  channel.stachpara.staHFCTPara;
                    jsonChannel.insert(STR_PARA_GAIN,stHFCTChannelPara.cGain);//增益
                    QJsonArray jsonGains;
                    jsonGains.append(0);
                    jsonGains.append(-20);
                    jsonGains.append(-40);
                    jsonGains.append(-60);
                    jsonChannel.insert(STR_PARA_GAIN_LIST, jsonGains);
                    jsonChannel.insert(STR_PARA_GAIN_MODEL, ConfigService::instance().listGainModel().at(stHFCTChannelPara.eChannelGainType));//增益类型
                    QJsonArray jsonGainModels;
                    QList<QString> gainModels = ConfigService::instance().listGainModel();
                    for (int i = 0; i < gainModels.size(); i++)
                    {
                        jsonGainModels.append(gainModels.at(i));
                    }
                    jsonChannel.insert(STR_PARA_GAIN_MODEL_LIST, jsonGainModels);
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD,stHFCTChannelPara.ucSampleCycles);//采样周期数
                    QJsonArray jsonSamplePeriods;
                    jsonSamplePeriods.append(50);
                    jsonSamplePeriods.append(60);
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
                    jsonChannel.insert(STR_PARA_SAMPLE_POINTS,stHFCTChannelPara.usSampelCount);//采样点数
                    QJsonArray jsonSamplePonits;
                    jsonSamplePonits.append(60);
                    jsonSamplePonits.append(72);
                    jsonChannel.insert(STR_PARA_SAMPLE_POINTS_LIST, jsonSamplePonits);
                }
                    break;
                case CHANNEL_MECH:
                {
                    MechChannelPara stMECHChannelPara =  channel.stachpara.staMechPara;
                    jsonChannel.insert(STR_PARA_LOOP_CURRENT_THRED,stMECHChannelPara.usLoopCurrentThred);//线圈电流阈值
                    jsonChannel.insert(STR_PARA_MOTOR_CURRENT_THRED,stMECHChannelPara.usMotorCurrentThred);//电机电流阈值
                    jsonChannel.insert(STR_PARA_SWITCH_STATE,int(stMECHChannelPara.bSwitchState));//开关量初始状态
                    jsonChannel.insert(STR_PARA_BREAK_TYPE,int(stMECHChannelPara.bBreakerType));//断路器机构类型
                    jsonChannel.insert(STR_PARA_MOTOR_FUNCTION_TYPE,int(stMECHChannelPara.bMotorFunctionType));//电机工作类型
                }
                    break;
                case CHANNEL_ARRESTER_I:
                case CHANNEL_GROUNDDINGCURRENT:
                case CHANNEL_LEAKAGECURRENT:
                {
                    ArresterIChannelPara stArresterIChannelPara =  channel.stachpara.staArresterIPara;
                    jsonChannel.insert(STR_PARA_CHANNEL_PHASE, stArresterIChannelPara.eChannelPhase);//
                }
                    break;
                case CHANNEL_ARRESTER_U:
                {
                    ArresterUChannelPara stArresterUChannelPara =  channel.stachpara.staArresterUPara;
                    jsonChannel.insert(STR_PARA_CHANNEL_PHASE,stArresterUChannelPara.eChannelPhase);//
                    jsonChannel.insert(STR_PARA_TRANSFORMATION_RATIO,stArresterUChannelPara.fTransformationRatio);//
                }
                    break;
                case CHANNEL_VIBRATION:
                {
                    QJsonArray jsonSamplePeriods;
                    jsonSamplePeriods.append(2);
                    jsonSamplePeriods.append(5);
                    jsonSamplePeriods.append(10);

                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD_LIST, jsonSamplePeriods);
                    jsonChannel.insert(STR_PARA_SAMPLE_RATE,channel.stachpara.staVibrationParam.usSampelCount);//采样点数
                    jsonChannel.insert(STR_PARA_SAMPLE_PERIOD,channel.stachpara.staVibrationParam.ucSampleCycles);//采样周期数
                }
                    break;
                case CHANNEL_SAW:
                {
                }
                    break;
                case CHANNEL_AX8:
                {
                }
                    break;
                case CHANNEL_TEMPERATURE:
                {
                    jsonChannel.insert("upThreshold", channel.stachpara.staTempParam.fUpperThreshold);//温度阈值上限
                    jsonChannel.insert("lowerThreshold", channel.stachpara.staTempParam.fLowerThreshold);//温度阈值下限
                    jsonChannel.insert("changeThreshold", channel.stachpara.staTempParam.fChangedThreshold);//温度变送阀值
                }
                    break;
                case CHANNEL_HUMIDITY:
                {
                    jsonChannel.insert("upThreshold", channel.stachpara.staHumParam.fUpperThreshold);//湿度阈值上限
                    jsonChannel.insert("lowerThreshold", channel.stachpara.staHumParam.fLowerThreshold);//湿度阈值下限
                    jsonChannel.insert("changeThreshold", channel.stachpara.staHumParam.fChangedThreshold);//湿度变送阀值
                }
                    break;
                case CHANNEL_FLOOD:
                {
                    jsonChannel.insert(STR_PARA_ALARM, channel.stachpara.stFloodChannelPara.cFlood);//水浸告警
                }
                    break;
                default:
                    break;
                }

            }
            jsonchannelList.append(jsonChannel);
        }
        if (!jsonchannelList.isEmpty())
        {
            data.insert(STR_ADU_CHANNEL_LIST, jsonchannelList);
        }

    }
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}


/************************************************
 * 函数名:  GetADUListApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端列表
 ************************************************/
void CommandServiceForApp::GetADUListApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others data;

    ConfigService &configService = ConfigService::instance();
    //获取ADU列表
    const QList<ADUUnitInfo>& aduList = configService.ADUList();

    //使用QHash按类型分组
    QHash<ADUType, QJsonArray> aduTypeMap;
    //aduTypeMap.reserve(ADU_TYPE_NUM);

    //遍历一次ADU列表,按类型分组
    for (const auto& adu : aduList)
    {
        QJsonObject aduInfoObj;
        aduInfoObj.insert(STR_ADU_NAME, adu.strName);
        aduInfoObj.insert(STR_ADU_ID, adu.strID);
        aduInfoObj.insert(STR_ADU_COMM_TYPE, configService.mapLinkGrp().key(adu.eLinkGroup));
        aduInfoObj.insert(STR_ADU_WORK_GROUP, adu.stADUParam.ucWorkGroup);

        aduTypeMap[adu.eType].append(aduInfoObj);
    }
    //遍历QHash生成最终数据结构
    for (const auto& aduType : aduTypeMap.keys())
    {
        const auto& aduInfos = aduTypeMap[aduType];
        QJsonObject aduTypeInfo;
        aduTypeInfo.insert(STR_ADU_TYPE_INDEX, static_cast<int>(aduType));
        aduTypeInfo.insert(STR_ADU_TYPE, configService.getADUTypName(aduType));
        aduTypeInfo.insert(STR_ADU_ITEMS, aduInfos);
        data.append(aduTypeInfo);
    }

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  saveChannelInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存通道信息
 ************************************************/
void CommandServiceForApp::saveChannelInfoApp(HttpRequest &request, HttpResponse &response)
{

    Q_UNUSED(response);
    Q_UNUSED(request);
    QMap<QString, QString> items;

    items.insert("strADUID",request.getParameter(STR_ADU_ID));

    ADUChannelType etype =  ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
    items.insert("etype",QString("%1").arg(etype));
    items.insert("unID",request.getParameter(STR_CHANNEL_INDEX));
    items.insert("strName",request.getParameter(STR_CHANNEL_NAME));

    switch (etype)
    {
    case CHANNEL_AE://AE参数
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg(ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        items.insert("cBackGroundNum",request.getParameter(STR_PARA_BACK_GROUND_DATA));
    }
        break;
    case CHANNEL_UHF:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("cBandWidth",QString("%1").arg(ConfigService::instance().getBandWidthEnum(request.getParameter(STR_PARA_FILTER))));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
    }
        break;
    case CHANNEL_HFCT:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
    }
        break;
    case CHANNEL_MECH:
    {
        items.insert("usLoopCurrentThred",request.getParameter(STR_PARA_LOOP_CURRENT_THRED));
        items.insert("usMotorCurrentThred",request.getParameter(STR_PARA_MOTOR_CURRENT_THRED));
        items.insert("bSwitchState",request.getParameter(STR_PARA_SWITCH_STATE));
        items.insert("bBreakerType",request.getParameter(STR_PARA_BREAK_TYPE));
        items.insert("bMotorFunctionType",request.getParameter(STR_PARA_MOTOR_FUNCTION_TYPE));
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
        items.insert("fTransformationRatio",request.getParameter(STR_PARA_TRANSFORMATION_RATIO));
    }
        break;
    case CHANNEL_VIBRATION:
    {
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
        items.insert("usSampelCount",request.getParameter(STR_PARA_SAMPLE_RATE));
    }
        break;
    case CHANNEL_SAW:
    {
    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));

    }
        break;
    case CHANNEL_FLOOD:
    {
        items.insert("cFlood",request.getParameter(STR_PARA_ALARM));
    }
        break;
    default:
        break;
    }

    emit sigSaveChannelInfoApp(items);

    responseWrite(QJsonDocument(encapsulationData()).toJson());
}


/************************************************
 * 函数名:  deleteADUInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除前端
 ************************************************/
void CommandServiceForApp::deleteADUInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);

    ADUType eType;
    bool bExist = ConfigService::instance().getADUTypeFromID(strADUID, eType);
    HTTPErrorCode eErrorCode = (HTTPErrorCode)ConfigService::instance().deleteADU(strADUID);
    if(ERROR_CODE_NONE_ERROR == eErrorCode && bExist)
    {
        if(MoistureController::isMoistureAdu(eType))
        {
            MoistureController::instance().removeTransmitter(strADUID);
        }
    }

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

/************************************************
 * 函数名:  saveADUInfOAndChannelInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存前端和通道信息详情
 ************************************************/
void CommandServiceForApp::saveADUInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QMap<QString, QString> items;

    ADUChannelType etype =  ConfigService::instance().getChanTypEnum(request.getParameter(STR_CHANNEL_TYPE));
    items.insert("eChannelType",QString("%1").arg(etype));
    items.insert("unID",request.getParameter(STR_CHANNEL_INDEX));
    items.insert("strChannelName",request.getParameter(STR_CHANNEL_NAME));

    switch (etype)
    {
    case CHANNEL_AE://AE参数
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg(ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        items.insert("cBackGroundNum",request.getParameter(STR_PARA_BACK_GROUND_DATA));
    }
        break;
    case CHANNEL_UHF:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("cBandWidth",QString("%1").arg(ConfigService::instance().getBandWidthEnum(request.getParameter(STR_PARA_FILTER))));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
    }
        break;
    case CHANNEL_HFCT:
    {
        items.insert("cGain",request.getParameter(STR_PARA_GAIN));
        items.insert("eChannelGainType",QString("%1").arg((ChannelGainType)ConfigService::instance().listGainModel().indexOf(request.getParameter(STR_PARA_GAIN_MODEL))));
    }
        break;
    case CHANNEL_MECH:
    {
        items.insert("usLoopCurrentThred",request.getParameter(STR_PARA_LOOP_CURRENT_THRED));
        items.insert("usMotorCurrentThred",request.getParameter(STR_PARA_MOTOR_CURRENT_THRED));
        items.insert("bSwitchState",request.getParameter(STR_PARA_SWITCH_STATE));
        items.insert("bBreakerType",request.getParameter(STR_PARA_BREAK_TYPE));
        items.insert("bMotorFunctionType",request.getParameter(STR_PARA_MOTOR_FUNCTION_TYPE));
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        items.insert("eChannelPhase",request.getParameter(STR_PARA_CHANNEL_PHASE));
        items.insert("fTransformationRatio",request.getParameter(STR_PARA_TRANSFORMATION_RATIO));
    }
        break;
    case CHANNEL_VIBRATION:
    {
        items.insert("ucSampleCycles",request.getParameter(STR_PARA_SAMPLE_PERIOD));
        items.insert("usSampelCount",request.getParameter(STR_PARA_SAMPLE_RATE));
    }
        break;
    case CHANNEL_SAW:
    {
    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        items.insert("fUpperThreshold",request.getParameter("upThreshold"));
        items.insert("fLowerThreshold",request.getParameter("lowerThreshold"));
        items.insert("fChangedThreshold",request.getParameter("changeThreshold"));

    }
        break;
    case CHANNEL_FLOOD:
    {
        items.insert("cFlood",request.getParameter(STR_PARA_ALARM));
    }
        break;
    default:
        break;
    }

    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    if(eADUType == ADU_TEMP_HUM_IS)  //温湿度传感器
    {
        eErrorCode = ERROR_CODE_NONE_ERROR;
        QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");

        QString strADUOldID = request.getParameterMap().contains(STR_ADU_OLD_ID) ?
                    request.getParameter(STR_ADU_OLD_ID).toUpper() :
                    strADUID;

        ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

        items.insert("strID",strADUID);
        items.insert("eType",QString("%1").arg(eADUType));
        items.insert("strADUOldID",strADUOldID);
        items.insert("strName",request.getParameter(STR_ADU_NAME));
        items.insert("eLinkGroup",QString("%1").arg((Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)))));
        items.insert("strRS485ComPort",request.getParameter(STR_ADU_RS485_COM_PORT));
        items.insert("iTaskGroup",request.getParameter(STR_SF6_ALARM_TASK_GROUP));
        items.insert("ucFrequency",request.getParameter(STR_FREQUNCY));
        items.insert("ucWorkGroup",request.getParameter(STR_ADU_WORK_GROUP));
        items.insert("ucConnectionLoad",request.getParameter(STR_ADU_COMM_LOAD));
        items.insert("ucConnectionSpeed",request.getParameter(STR_ADU_COMM_SPEED));
        items.insert("uiSleepTime",request.getParameter(STR_ADU_SLEEP_TIME));
        items.insert("uiSampleSpace",request.getParameter(STR_ADU_SAMPLE_SPACE));
        items.insert("usStartSampleTime",request.getParameter(STR_ADU_SAMPEL_START_TIMR));
        items.insert("ucAutoUpdate",request.getParameter(STR_ADU_B_AUTO_GIVING));
        Monitor::ADUWorkMode eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
        items.insert("eADUWorkModel",QString("%1").arg(eADUWorkModel));
        items.insert("uiAutoGivingSpace",request.getParameter(STR_ADU_AUTO_GIVISPACE));
        emit sigSaveADUInfoAndChannelInfoApp(items);
        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
    }
    else
    {
        QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");
        QString strADUOldID = request.getParameter(STR_ADU_OLD_ID).toUpper();

        if (ConfigService::instance().isADUIDExisted(strADUID) && (strADUOldID != strADUID))
        {
            eErrorCode = ERROR_CODE_CONFIG_ADU_ID_EXISTED;   //前端已经存在
            responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
            return;
        }
        else if (!ConfigService::instance().isADUIDExisted(strADUOldID) && !strADUOldID.isEmpty())
        {
            eErrorCode = ERROR_CODE_CONFIG_ADU_ID_NOT_EXISTED;  //前端不存在
            responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
            return;
        }
        items.insert("strID",strADUID);
        items.insert("eType",QString("%1").arg(eADUType));
        items.insert("strADUOldID",strADUOldID);
        items.insert("strName",request.getParameter(STR_ADU_NAME));
        items.insert("eLinkGroup",QString("%1").arg((Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)))));
        items.insert("strRS485ComPort",request.getParameter(STR_ADU_RS485_COM_PORT));
        items.insert("iTaskGroup",request.getParameter(STR_SF6_ALARM_TASK_GROUP));
        items.insert("ucFrequency",request.getParameter(STR_FREQUNCY));
        items.insert("ucWorkGroup",request.getParameter(STR_ADU_WORK_GROUP));
        items.insert("ucConnectionLoad",request.getParameter(STR_ADU_COMM_LOAD));
        items.insert("ucConnectionSpeed",request.getParameter(STR_ADU_COMM_SPEED));
        items.insert("uiSleepTime",request.getParameter(STR_ADU_SLEEP_TIME));
        items.insert("uiSampleSpace",request.getParameter(STR_ADU_SAMPLE_SPACE));
        items.insert("usStartSampleTime",request.getParameter(STR_ADU_SAMPEL_START_TIMR));
        items.insert("ucAutoUpdate",request.getParameter(STR_ADU_B_AUTO_GIVING));
        Monitor::ADUWorkMode eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
        items.insert("eADUWorkModel",QString("%1").arg(eADUWorkModel));
        items.insert("uiAutoGivingSpace",request.getParameter(STR_ADU_AUTO_GIVISPACE));
        items.insert("extendInfo",request.getParameter(STR_ADU_EXTEND));

        emit sigSaveADUInfoAndChannelInfoApp(items);
        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
    }
}

/************************************************
 * 函数名:  addADUInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  新增前端
 ************************************************/
void CommandServiceForApp::addADUInfoApp(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QMap<QString, QString> items;

    ADUType eADUType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    if(eADUType == ADU_TEMP_HUM_IS)  //温湿度传感器
    {
        eErrorCode = ERROR_CODE_NONE_ERROR;
        QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");

        QString strADUOldID = request.getParameterMap().contains(STR_ADU_OLD_ID) ?
                    request.getParameter(STR_ADU_OLD_ID).toUpper() :
                    strADUID;

        ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

        items.insert("strID",strADUID);
        items.insert("eType",QString("%1").arg(eADUType));
        items.insert("strADUOldID",strADUOldID);
        items.insert("strName",request.getParameter(STR_ADU_NAME));
        items.insert("eLinkGroup",QString("%1").arg((Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)))));
        items.insert("strRS485ComPort",request.getParameter(STR_ADU_RS485_COM_PORT));
        items.insert("iTaskGroup",request.getParameter(STR_SF6_ALARM_TASK_GROUP));
        items.insert("ucFrequency",request.getParameter(STR_FREQUNCY));
        items.insert("ucWorkGroup",request.getParameter(STR_ADU_WORK_GROUP));
        items.insert("ucConnectionLoad",request.getParameter(STR_ADU_COMM_LOAD));
        items.insert("ucConnectionSpeed",request.getParameter(STR_ADU_COMM_SPEED));
        items.insert("uiSleepTime",request.getParameter(STR_ADU_SLEEP_TIME));
        items.insert("uiSampleSpace",request.getParameter(STR_ADU_SAMPLE_SPACE));
        items.insert("usStartSampleTime",request.getParameter(STR_ADU_SAMPEL_START_TIMR));
        items.insert("ucAutoUpdate",request.getParameter(STR_ADU_B_AUTO_GIVING));
        Monitor::ADUWorkMode eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
        items.insert("eADUWorkModel",QString("%1").arg(eADUWorkModel));
        items.insert("uiAutoGivingSpace",request.getParameter(STR_ADU_AUTO_GIVISPACE));

        emit sigAddADUInfoApp(items);
        responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
        return;
    }
    else
    {
        QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");
        QString strADUOldID = request.getParameter(STR_ADU_OLD_ID).toUpper();

        if (ConfigService::instance().isADUIDExisted(strADUID) && (strADUOldID != strADUID))
        {
            eErrorCode = ERROR_CODE_CONFIG_ADU_ID_EXISTED;   //前端已经存在
            responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
            return;
        }
        else if (!ConfigService::instance().isADUIDExisted(strADUOldID) && !strADUOldID.isEmpty())
        {
            eErrorCode = ERROR_CODE_CONFIG_ADU_ID_NOT_EXISTED;  //前端不存在
            responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
            return;
        }
        ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

        items.insert("strID",strADUID);
        items.insert("eType",QString("%1").arg(eADUType));
        items.insert("strADUOldID",strADUOldID);
        items.insert("strName",request.getParameter(STR_ADU_NAME));
        items.insert("eLinkGroup",QString("%1").arg((Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)))));
        items.insert("strRS485ComPort",request.getParameter(STR_ADU_RS485_COM_PORT));
        items.insert("iTaskGroup",request.getParameter(STR_SF6_ALARM_TASK_GROUP));
        items.insert("ucFrequency",request.getParameter(STR_FREQUNCY));
        items.insert("ucWorkGroup",request.getParameter(STR_ADU_WORK_GROUP));
        items.insert("ucConnectionLoad",request.getParameter(STR_ADU_COMM_LOAD));
        items.insert("ucConnectionSpeed",request.getParameter(STR_ADU_COMM_SPEED));
        items.insert("uiSleepTime",request.getParameter(STR_ADU_SLEEP_TIME));
        items.insert("uiSampleSpace",request.getParameter(STR_ADU_SAMPLE_SPACE));
        items.insert("usStartSampleTime",request.getParameter(STR_ADU_SAMPEL_START_TIMR));
        items.insert("ucAutoUpdate",request.getParameter(STR_ADU_B_AUTO_GIVING));
        Monitor::ADUWorkMode eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
        items.insert("eADUWorkModel",QString("%1").arg(eADUWorkModel));
        items.insert("uiAutoGivingSpace",request.getParameter(STR_ADU_AUTO_GIVISPACE));
        items.insert("extendInfo",request.getParameter(STR_ADU_EXTEND));

        emit sigAddADUInfoApp(items);
    }
}

/************************************************
 * 输入参数:
 *  request -- http请求
 * 功能:  保存温湿度传感器
 ************************************************/
bool CommandServiceForApp::saveTempHumADUApp(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QString strADUID = request.getParameter(STR_ADU_ID).toUpper().replace(" ", "");
    //safe::AuditManager::instance().addAudit("user", safe::AUDIT_ADU_SET, QDateTime::currentDateTime(), strADUID);
    QString strADUOldID = request.getParameterMap().contains(STR_ADU_OLD_ID) ?
                request.getParameter(STR_ADU_OLD_ID).toUpper() :
                strADUID;
    //    QString strADUOldID = request.getParameter(STR_ADU_OLD_ID).toUpper();

    ADUType eADUType =  ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    ADUUnitInfo adu;

    ConfigService::instance().getADU(strADUID, eADUType, adu);
    adu.strID = strADUID;
    adu.eType = eADUType;
    adu.strName = request.getParameter(STR_ADU_NAME);
    adu.eLinkGroup = (Monitor::LinkGroup)ConfigService::instance().mapLinkGrp().value(QString(request.getParameter(STR_ADU_COMM_TYPE)));
    adu.strRS485ComPort = request.getParameter(STR_ADU_RS485_COM_PORT);
    adu.iTaskGroup = request.getParameter(STR_SF6_ALARM_TASK_GROUP).toInt();

    adu.stADUParam.ucFrequency = request.getParameter(STR_FREQUNCY).toInt();
    adu.stADUParam.ucWorkGroup = request.getParameter(STR_ADU_WORK_GROUP).toInt();
    adu.stADUParam.ucConnectionLoad = request.getParameter(STR_ADU_COMM_LOAD).toInt();
    adu.stADUParam.ucConnectionSpeed = request.getParameter(STR_ADU_COMM_SPEED).toInt();
    adu.stADUParam.uiSleepTime = request.getParameter(STR_ADU_SLEEP_TIME).toInt();
    adu.stADUParam.uiSampleSpace = request.getParameter(STR_ADU_SAMPLE_SPACE).toInt();
    adu.stADUParam.usStartSampleTime = request.getParameter(STR_ADU_SAMPEL_START_TIMR).toInt();
    adu.stADUParam.ucAutoUpdate = request.getParameter(STR_ADU_B_AUTO_GIVING).toInt();
    adu.stADUParam.eADUWorkModel = ConfigService::instance().getADUWorkModeEnum(request.getParameter(STR_ADU_MODE));
    adu.stADUParam.uiAutoGivingSpace = request.getParameter(STR_ADU_AUTO_GIVISPACE).toInt();
    adu.stADUParam.eLinkGroup = adu.eLinkGroup;
    if ( Monitor::WORKER_MODEL_LOWPOWER == adu.stADUParam.eADUWorkModel )
    {
        adu.stADUParam.ucStartArtificialTime = 8;
        adu.stADUParam.ucEndArtificialTime = 20;
        adu.stADUParam.usArtificialWakeUpInterval = 3;//request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
        adu.stADUParam.usNotArtificialWalkUpInterval = 30;//request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
        adu.stADUParam.bAutoChangeMode = 1;//request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE).toInt();
    }
#ifndef Q_WS_QWS
    ConfigService::instance().saveADUInfo(adu);
#endif
    ConfigService::instance().saveADUName(adu.strID, adu.strName);

    //前端存在，此时为修改前端
    if(ConfigService::instance().isADUIDExisted(strADUOldID))
    {
        if(strADUOldID != strADUID)  //更改了前端ID
        {
            ConfigService::instance().setAduID(strADUOldID, strADUID);
        }
    }
    else //前端不存在，按照默认参数本地存储,防止后续操作找不到前端
    {
        ADUUnitInfo defaultAdu = adu;
        defaultAdu.stADUParam.uiSleepTime = 1;
        defaultAdu.stADUParam.eADUWorkModel = Monitor::WORKER_MODEL_MAINTAIN;
        ConfigService::instance().addADUInfo(defaultAdu);
        adu.stADUParam.usNumInGroup = defaultAdu.stADUParam.usNumInGroup;
    }

    MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam);

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

    return true;
}

/************************************************
 * 函数名:  GetPointNameListApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取指定设备下的测点列表
 ************************************************/
void CommandServiceForApp::GetPointNameListApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    Others OthersData;
    QString strDeviceID = request.getParameter(STR_DEVICE_ID);

    OthersData.GetPointNameListApp(strDeviceID);

    responseWrite(QJsonDocument(encapsulationData(OthersData)).toJson());
}

/************************************************
 * 函数名:  GetDeviceNameListApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取设备名称列表
 ************************************************/
void CommandServiceForApp::GetDeviceNameListApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.getDeviceNameListApp();
    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}

/************************************************
 * 函数名:  saveRelation
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获保存关联信息
 ************************************************/
void CommandServiceForApp::saveRelationApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response)
    Q_UNUSED(request)

    ConfigService &configService = ConfigService::instance();
    QString strPointName =  request.getParameter(STR_TEST_POINT_NAME);
    PDS_SYS_INFO_LOG("CommandServiceForApp:saveRelationApp, strPointName :%s", strPointName.toLatin1().data());
    int jsonChannels = request.getParameter("aduChannelsLength").toInt();
    QList<PointConnectionInfo> listConnectionInfos;
    for (int i = 0; i < jsonChannels; i++)
    {
        PointConnectionInfo ConnectionInfo;
        ADUChannelType chaType = ConfigService::instance().getChanTypEnum(request.getParameter(QString("%1[%2][%3]").arg(STR_ADU_CHANNELS).arg(i).arg(STR_CHANNEL_TYPE).toUtf8()));
        ConnectionInfo.etype = chaType;
        ConnectionInfo.strID = request.getParameter(QString("%1[%2][%3]").arg(STR_ADU_CHANNELS).arg(i).arg(STR_ADU_ID).toUtf8());
        ConnectionInfo.unID = request.getParameter(QString("%1[%2][%3]").arg(STR_ADU_CHANNELS).arg(i).arg(STR_CHANNEL_INDEX).toUtf8()).toInt();
        PDS_SYS_INFO_LOG("CommandServiceForApp:saveRelationApp, ConnectionInfo.strID :%s", ConnectionInfo.strID.toLatin1().data());
        listConnectionInfos.append(ConnectionInfo);
    }
    int jsonPoints = request.getParameter("testPointLength").toInt();
    for (int i = 0; i < jsonPoints; i++)
    {
        QString strPointId = request.getParameter(QString("%1[%2][%3]").arg(STR_TEST_POINT).arg(i).arg(STR_TEST_POINT_ID).toUtf8());
        PDS_SYS_INFO_LOG("CommandServiceForApp:saveRelationApp, strPointId :%s", strPointId.toLatin1().data());
        configService.saveRelationInfo(strPointId, strPointName, listConnectionInfos);
    }
    responseWrite(QJsonDocument(encapsulationData()).toJson());
}

/************************************************
 * 函数名:  getChannelListApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通道列表
 ************************************************/
void CommandServiceForApp::getChannelListApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    QString strADUID = request.getParameter(STR_ADU_ID);

    Others data;
    data.getChannelList(strADUID);

    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  GetADUVersionListApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取前端版本号信息
 ************************************************/
void CommandServiceForApp::GetADUVersionListApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    QJsonArray data = configData.GetADUVersionList(request.getParameter(STR_ADU_TYPE));
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}

/************************************************
 * 函数名:  DeletePointInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除测点信息
 ************************************************/
void CommandServiceForApp::DeletePointInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();

    HTTPErrorCode eErrorCode = (HTTPErrorCode)configs.delTestPoint(request.getParameter(STR_POINT_ID));

    PDS_SYS_INFO_LOG("CommandServiceForApp:Delete point info, result code :%s", request.getParameter(STR_POINT_ID).data());
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}

/************************************************
 * 函数名:  SavePointInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存测点信息
 ************************************************/
void CommandServiceForApp::SavePointInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();
    TestPointInfo stPoint;
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    stPoint.strName = request.getParameter(STR_POINT_NAME);
    stPoint.strPointGUID = request.getParameter(STR_POINT_ID);

    QString strDeviceID = request.getParameter(STR_DEVICE_ID);

    if (stPoint.strPointGUID.isEmpty())
    {
        stPoint.strPointGUID = configs.getGUID();
        eErrorCode = (HTTPErrorCode)configs.addTestPoint(strDeviceID, stPoint);
        PDS_SYS_INFO_LOG("CommandServiceForApp:Begin to add point : %s", stPoint.strPointGUID.toLatin1().data());
    }
    else
    {
        eErrorCode = (HTTPErrorCode)configs.updateTestPoint(strDeviceID, stPoint);
        PDS_SYS_INFO_LOG("CommandServiceForApp:Begin to add point : %s", stPoint.strPointGUID.toLatin1().data());
    }
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}


/************************************************
 * 函数名:  DeleteDeviceInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  删除指定设备
 ************************************************/
void CommandServiceForApp::DeleteDeviceInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    ConfigService &configs = ConfigService::instance();
    eErrorCode = (HTTPErrorCode)configs.delDevice(request.getParameter(STR_DEVICE_ID));

    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());

}
/************************************************
 * 函数名:  SaveDeviceInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存设备详细信息
 ************************************************/
void CommandServiceForApp::SaveDeviceInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();
    DeviceNode device;
    device.strName = request.getParameter(STR_DEVICE_NAME);
    device.strDeviceGUID = request.getParameter(STR_DEVICE_ID);
    device.strPMS = request.getParameter(STR_DEVICE_PMS);

    QList<QString> strDevice = ConfigService::instance().listDeviceTypeName();
    device.eDeviceType = (DeviceType)(strDevice.indexOf(request.getParameter(STR_DEVICE_TYPE_NAME)) + 1);
    QList<QString> voltage = ConfigService::instance().listVoltageLevelName();
    device.eVoltage = (VoltageLevel)voltage.indexOf(request.getParameter(STR_DEVICE_VOL_LEVEL));

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    if (device.strDeviceGUID.isEmpty())
    {
        device.strDeviceGUID = configs.getGUID();
        if(CONFIG_DEVICE_EXISTED == configs.addDevice( device ))
        {
            responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_DEVICE_EXISTED)).toJson());
            return;
        }
        PDS_SYS_INFO_LOG("CommandServiceForApp:Begin to add device %s", device.strDeviceGUID.toLatin1().data());
    }
    else
    {
        eErrorCode = (HTTPErrorCode)configs.updateDevice( device );
        PDS_SYS_INFO_LOG("CommandServiceForApp:Begin to update device :%s", device.strDeviceGUID.toLatin1().data());
    }
    responseWrite(QJsonDocument(encapsulationData(eErrorCode)).toJson());
}

/************************************************
 * 函数名:  SaveStationInfoApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  保存站点信息
 ************************************************/
void CommandServiceForApp::SaveStationInfoApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    ConfigService &configs = ConfigService::instance();

    StationNode strStationNode;
    strStationNode.strName = request.getParameter(STR_STATION_NAME);
    QList<QString> voltage = ConfigService::instance().listVoltageLevelName();
    strStationNode.eVoltage = (VoltageLevel)voltage.indexOf(request.getParameter(STR_STATION_VOLTAGE));
    strStationNode.strCompany = request.getParameter(STR_STATION_COMPANY);
    strStationNode.strPMS = request.getParameter(STR_STATION_PMS);

    configs.saveStationInfo(strStationNode);

    //发送信号至数据库
    QString newGUID = ConfigService::instance().stationNode().strSiteGUID;
    PDS_SYS_INFO_LOG("CommandServiceForApp:SaveStationInfo %s", newGUID.toLatin1().data());
    emit  sigStationMD5(newGUID);

    responseWrite(QJsonDocument(encapsulationData()).toJson());

}

/************************************************
 * 函数名:  GetStationApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取站点信息
 ************************************************/
void CommandServiceForApp::GetStationApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    ConfigData configData;
    configData.getStation();

    responseWrite(QJsonDocument(encapsulationData(configData)).toJson());
}


/************************************************
 * 函数名:  GetCommonTypeApp
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能:  获取通用枚举信息
 ************************************************/
void CommandServiceForApp::GetCommonTypeApp(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(response);
    CommonEnumData eEnumData = (CommonEnumData)request.getParameter(STR_TYPE).toInt();

    Others data;
    switch (eEnumData) {
    case VOL_LEVEL_ENUM:
        data.getVolLevelList();
        break;
    case DEVICE_TYPE_ENUM:
        data.getDeviceTypeList();
        break;
    case ADU_TYPE_ENUM:
        data.getaduTypeList();
        break;
    case LINK_TYPE_ENUM:
        data.getLinkTypeList();
        break;
    case CHANNEL_TYPE_ENUM:
        data.getChannelTypeList();
        break;
    case RS485_PORT_ENUM:
        data.getRS485ComPortList();
        break;
    case ADU_WORK_TYPE_ENUM:
        data.getADUWorkModeList();
        break;
    case ADU_LOWER_POWER_WAKE_UP_SPACE:
        data.getLowerPowerWakeIpSpace();
        break;
    default:

        break;
    }
    //通知页面的操作结果
    responseWrite(QJsonDocument(encapsulationData(data)).toJson());
}


/************************************************
* 功能:  获取单个传感器信息
* 输入参数:  request -- http请求
*      response -- 响应
************************************************/
void CommandServiceForApp::getSingleSensorsConfig( HttpRequest &request, HttpResponse &response )
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    const QString strGetSensorID = request.getParameter("sensorId");
    if(strGetSensorID.isEmpty())
    {
        logWarnning("getSingleSensorsConfig, sensorId is empty");

        responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_PARAMETER_ERROR)).toJson());
        return;
    }
    logInfo(QString("getSingleSensorsConfig, sensorId:%1").arg(strGetSensorID));

    QJsonObject sensorInfo;
    ConfigService& configService = ConfigService::instance();
    const QList<DeviceNode>& deviceList = configService.getStationNode().devices;
    bool bFindSensor = false; //是否找到传感器
    for(const auto &deviceTmp : deviceList) //一次设备  
    {
        for(const auto &tpInfoTmp : deviceTmp.testPoints) //一次设备下的测点
        {
            const auto &pointConnectList = tpInfoTmp.ConnectionInfo; //测点的关联信息
            if(pointConnectList.isEmpty())
            {
                continue;
            }
            const auto &firstConnection = pointConnectList.first();
            const ADUChannelType eCurrentChannelType = firstConnection.etype; //当前测点的通道类型
            const QString &strCurrentSensorID = firstConnection.strID; //当前测点关联的传感器ID

            //设置处理的通道类型uhf,hfct,ae,tev
            const QSet<ADUChannelType> channelTypeSet{CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_AE, CHANNEL_TEV};
            if(channelTypeSet.contains(eCurrentChannelType) && strCurrentSensorID == strGetSensorID)
            {
                DBServer& dbServer =  DBServer::instance();
                //数据条数
                const int dataCount = dbServer.totalDataNumInTable(tpInfoTmp.strPointGUID, eCurrentChannelType);

                //获取最新传感器状态
                int batteryVoltage = 0; //电池电压
                int signalStrength = 0, snr = 0; //信号强度，信噪比
                common::SensorStatus sensorStatus;
                dbServer.getLatestSensorStatus(tpInfoTmp.strPointGUID, sensorStatus);

                batteryVoltage = sensorStatus.iBatteryVoltage;
                signalStrength = sensorStatus.iSignalStrength;
                snr = sensorStatus.iSnr;

                QJsonObject sensorBatteryInfo;        //电池信息
                QJsonObject sensorCommunication;      //通信状态信息
                sensorInfo.insert("sensorId", strGetSensorID);
                sensorInfo.insert("dataNumber", dataCount);
                sensorInfo.insert("locationName", deviceTmp.strName);

                sensorBatteryInfo.insert("batteryVoltage", batteryVoltage);
                sensorCommunication.insert("signalStrength", signalStrength);
                sensorCommunication.insert("SNR", snr);

                sensorInfo.insert("battery", sensorBatteryInfo);
                sensorInfo.insert("communication", sensorCommunication);
                
                bFindSensor = true;
                break;
            }
        }
        if(bFindSensor)
        {
            break;
        }
    }

    responseWrite(QJsonDocument(encapsulationData(sensorInfo, eErrorCode)).toJson());
}


/************************************************
* 功能:  获取所有传感器的运维信息
* 输入参数:  request -- http请求
*      response -- 响应
************************************************/
void CommandServiceForApp::getAllSensorsConfig( HttpRequest &request, HttpResponse &response )
{
    Q_UNUSED(response);
    Q_UNUSED(request);

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    ConfigService& configService = ConfigService::instance();
    const QList<DeviceNode>& deviceList = configService.getStationNode().devices;

    QJsonArray sensorInfoArray;
    //已添加的传感器
    QStringList addedSensorList;

    for(const auto &deviceTmp : deviceList) //一次设备
    {
        for(const auto &tpInfoTmp : deviceTmp.testPoints) //一次设备下的测点
        {
            const auto &pointConnectList = tpInfoTmp.ConnectionInfo; //测点的关联信息
            if(pointConnectList.isEmpty())
            {
                logWarnning(QString("device:%1, testPoint:%2, no connection info").arg(deviceTmp.strName).arg(tpInfoTmp.strOutName));
                continue;
            }
            const auto &firstConnection = pointConnectList.first();
            const ADUChannelType eCurrentChannelType = firstConnection.etype; //当前测点的通道类型
            const QString &strSensorID = firstConnection.strID; //当前测点关联的传感器ID

            //已添加的传感器
            if(addedSensorList.contains(strSensorID))
            {
                continue;
            }
            //三合一传感器温度测点不处理
            if (CHANNEL_TEMPERATURE == eCurrentChannelType)
            {
                ADUUnitInfo stADUInfo;
                if (!ConfigService::instance().getADU(strSensorID, stADUInfo) || stADUInfo.eType == ADU_PD_IS) {
                    continue;
                }
            }

            DBServer& dbServer =  DBServer::instance();
            const int dataCount = dbServer.totalDataNumInTable(tpInfoTmp.strPointGUID, eCurrentChannelType); //数据条数

            //获取最新传感器状态
            int batteryVoltage = 0; //电池电压
            int signalStrength = 0, snr = 0; //信号强度，信噪比

            //设置处理的通道类型uhf,hfct,ae,tev
            const QSet<ADUChannelType> channelTypeSet{CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_AE, CHANNEL_TEV};
            if(channelTypeSet.contains(eCurrentChannelType))
            {
                common::SensorStatus sensorStatus;
                dbServer.getLatestSensorStatus(tpInfoTmp.strPointGUID, sensorStatus);

                batteryVoltage = sensorStatus.iBatteryVoltage;
                signalStrength = sensorStatus.iSignalStrength;
                snr = sensorStatus.iSnr;
            }
            else
            {
                logWarnning(QString("device:%1, testPoint:%2, no support channel type:%3").arg(deviceTmp.strName).arg(tpInfoTmp.strOutName).arg(eCurrentChannelType));
            }

            QJsonObject sensorInfo;
            QJsonObject sensorBatteryInfo;        //电池信息
            QJsonObject sensorCommunication;      //通信状态信息

            sensorInfo.insert("sensorId", strSensorID);
            sensorInfo.insert("dataNumber", dataCount);
            sensorInfo.insert("locationName", deviceTmp.strName);

            sensorBatteryInfo.insert("batteryVoltage", batteryVoltage);
            sensorCommunication.insert("signalStrength", signalStrength);
            sensorCommunication.insert("SNR", snr);
            sensorInfo.insert("battery", sensorBatteryInfo);
            sensorInfo.insert("communication", sensorCommunication);
            sensorInfoArray.append(sensorInfo);

            addedSensorList.append(strSensorID);
        }
    }

    responseWrite(QJsonDocument(encapsulationData(sensorInfoArray, eErrorCode)).toJson());
}

/************************************************
* 功能:  更新单个传感器信息
* 输入参数:  request -- http请求
*      response -- 响应
************************************************/
void CommandServiceForApp::updateSingleSensorsConfig( HttpRequest &request, HttpResponse &response )
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QString strADUID = request.getParameter("sensorId");
    ADUType eADUType;
    ConfigService::instance().getADUTypeFromID(strADUID, eADUType);

    QJsonObject jsonData;
    //bool bRet =  MonitorService::instance().sample(strADUID, eADUType) ;
    emit sigSampleCommand(strADUID);
    jsonData.insert("result", true);
    jsonData.insert("sensorId", strADUID);

    responseWrite(QJsonDocument(encapsulationData(jsonData,eErrorCode)).toJson());
}

/************************************************
* 功能:  更新所有传感器的运维信息
* 输入参数:  request -- http请求
*      response -- 响应
************************************************/
void CommandServiceForApp::updateAllSensorsConfig( HttpRequest &request, HttpResponse &response )
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    ConfigService &configService = ConfigService::instance();
    const QList<ADUUnitInfo> &adus =  configService.ADUList();
/*
    foreach(ADUUnitInfo tmpInfo,adus)
    {
        MonitorService::instance().sample(tmpInfo.strID, tmpInfo.eType) ;
    }
*/
    QString qstrID;
    emit sigSampleCommand(qstrID);
    QJsonObject jsonData;
    jsonData.insert("result", true);

    responseWrite(QJsonDocument(encapsulationData(jsonData,eErrorCode)).toJson());
}

/************************************************
* 功能:  获取传感器的历史信号强度和信噪比
* 输入参数:  request -- http请求
*      response -- 响应
************************************************/
void CommandServiceForApp::getSensorSignalTrend( HttpRequest &request, HttpResponse &response )
{
    Q_UNUSED(response);
    Q_UNUSED(request);
    const QString strGetSensorID = request.getParameter("sensorId");
    if(strGetSensorID.isEmpty())
    {
        logWarnning("getSensorSignalTrend, sensorId is empty");
        responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_PARAMETER_ERROR)).toJson());
        return;
    }
    const QDateTime startTime = QDateTime::fromString(request.getParameter("startTime"), STR_DATE_TIME_FORMAT);
    const QDateTime endTime = QDateTime::fromString(request.getParameter("endTime"), STR_DATE_TIME_FORMAT);
    if(!startTime.isValid() || !endTime.isValid())
    {
        logWarnning("getSensorSignalTrend, startTime or endTime is invalid");
        responseWrite(QJsonDocument(encapsulationData(ERROR_CODE_PARAMETER_ERROR)).toJson());
        return;
    }

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;
    QJsonArray signalTrendArray;

    ConfigService &configService = ConfigService::instance();
    DBServer &dbServer = DBServer::instance();
    const QList<DeviceNode> &deviceList = configService.getStationNode().devices;
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_AE, CHANNEL_TEV}; //支持的通道类型
    bool bFindSensor = false;
    for(const auto &deviceTmp : deviceList)
    {
        for(const auto &tpTmp : deviceTmp.testPoints)
        {
            const auto &pointConnectList = tpTmp.ConnectionInfo; //测点的关联信息
            if(pointConnectList.isEmpty())
            {
                continue;
            }
            const auto &firstConnection = pointConnectList.first();
            const ADUChannelType eCurrentChannelType = firstConnection.etype; //当前测点的通道类型
            const QString &strCurrentSensorID = firstConnection.strID; //当前测点关联的传感器ID
            if(channelTypeSet.contains(eCurrentChannelType) && strCurrentSensorID == strGetSensorID)
            {
                QVector<common::SensorStatus> sensorStatusList;
                dbServer.getSensorStatusByTimeRange(tpTmp.strPointGUID, startTime, endTime, sensorStatusList, 1, 10000*100);
                for(const auto &sensorStatusTmp : sensorStatusList)
                {
                    QJsonObject signalTrendItem;
                    signalTrendItem.insert("dateTime", sensorStatusTmp.recordTime.toString(STR_DATE_TIME_FORMAT));
                    signalTrendItem.insert("signalStrength", sensorStatusTmp.iSignalStrength);
                    signalTrendItem.insert("SNR", sensorStatusTmp.iSnr);
                    signalTrendArray.append(signalTrendItem);
                }
                bFindSensor = true;
                break;
            }
        }
        if(bFindSensor)
        {
            break;
        }
    }
    result.insert("sensorId", strGetSensorID);
    result.insert("signalTrend", signalTrendArray);
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson());
}

void CommandServiceForApp::getSyncDataADUType(HttpRequest &request, HttpResponse &response)
{
    Others otherData;
    otherData.GetSyncDataADUType();
    responseWrite(QJsonDocument(encapsulationData(otherData)).toJson());
}

void CommandServiceForApp::syncAduDataForApp(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;

    //请求参数解析
    QStringList aduList =  QString(request.getParameter(STR_ADU_LIST)).split(',');
    QDateTime startTime = QDateTime(QDate::fromString(request.getParameter(STR_BEGIN_DATE), STR_DATE_QSTRING_CNA));
    QDateTime endTime = QDateTime(QDate::fromString(request.getParameter(STR_END_DATE), STR_DATE_QSTRING_CNA).addDays(1)).addSecs(-1);
    ADUType aduType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));

    logInfo(QString("CommandServiceForApp::syncAduDataForApp--"
                    "adulist:%1, startTime:%2, endTime:%3, aduType:%4").
            arg(QString(request.getParameter(STR_ADU_LIST))).
            arg(QString(request.getParameter(STR_BEGIN_DATE))).
            arg(QString(request.getParameter(STR_END_DATE))).
            arg(QString(request.getParameter(STR_ADU_TYPE))));

    logInfo(QString("CommandServiceForApp::syncAduDataForApp--"
                    "adulist:%1, startTime:%2, endTime:%3, aduType:%4").arg(aduList.join(',')).
            arg(startTime.toString(STR_DATE_TIME_QSTRING_CNA)).arg(endTime.toString(STR_DATE_TIME_QSTRING_CNA)).arg(int(aduType)));

    if(startTime.isValid() && endTime.isValid())
    {
        //操作记录
        m_syncRecord.eLastSyncAduType = aduType;
        m_syncRecord.lastSyncAduList = aduList;
        m_syncRecord.lastSyncAduTime = QDateTime::currentDateTime();

        bool bRet = MonitorService::instance().syncData(aduList, aduType, startTime, endTime);
        if(!bRet)
        {
            eErrorCode = ERROR_CODE_SYNCDATA_ERROR;
        }
    }
    else
    {
        eErrorCode = ERROR_CODE_PARAMETER_ERROR;
    }


    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::cancelSyncAduDataForApp(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;

    if(ADU_TYPE_UNKNOW != m_syncRecord.eLastSyncAduType)
    {
        MonitorService::instance().stopSyncData(m_syncRecord.eLastSyncAduType);
    }

    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getAduSyncDataStatusForApp(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;

    if(ADU_TYPE_UNKNOW == m_syncRecord.eLastSyncAduType)
    {
        result.insert("syncStatus", QJsonValue(0));
        result.insert("aduStatusList", QJsonValue(QJsonArray()));
    }
    else
    {
        result = MonitorService::instance().getSyncDataStatus(m_syncRecord.eLastSyncAduType).toJsonObject();
    }
    result.insert("aduType", QJsonValue(ConfigService::instance().getADUTypName(m_syncRecord.eLastSyncAduType)));

    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getUpgradeAduInfo(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;

    QString strAduTypeName = request.getParameter(STR_ADU_TYPE);
    ADUType eType = ConfigService::instance().getADUTypEnum(strAduTypeName);
    logInfo("get adu info type is: ") << strAduTypeName;

    QJsonObject result;
    QJsonArray aduItems;

    QList<ADUUnitInfo> aduList = ConfigService::instance().ADUList(eType);
    int aduItemId = 0;
    for(auto iter = aduList.cbegin(); iter != aduList.cend(); ++iter)
    {
        QJsonObject aduInfo;
        aduInfo.insert(STR_ADU_ID, (*iter).strID);
        aduInfo.insert(STR_ADU_NAME, (*iter).strName);
        aduInfo.insert("version", (*iter).strVersion);

        //获取设备和测点信息
        QString strDeviceName, strDeviceId, strLocationName;
        const StationNode deviceTree = ConfigService::instance().stationNode();
        for(auto i = 0; i < deviceTree.devices.size(); i++)   //查找设备
        {
            for(auto j = 0; j < deviceTree.devices[i].testPoints.size(); j++)   //查找测点
            {
                for(auto z = 0; z < deviceTree.devices[i].testPoints[j].ConnectionInfo.size(); z++)  //查找测点关联设备
                {
                    if(deviceTree.devices[i].testPoints[j].ConnectionInfo[z].strID == (*iter).strID)
                    {
                        strDeviceName = deviceTree.devices[i].strName;
                        strDeviceId = deviceTree.devices[i].strPMS;
                        strLocationName = deviceTree.devices[i].testPoints[j].strName;
                        aduInfo.insert("deviceName", strDeviceName);
                        aduInfo.insert("deviceId", strDeviceId);
                        aduInfo.insert("locationName", strLocationName);
                        aduInfo.insert("itemId", aduItemId++);
                        aduItems.append(aduInfo);
                        break;
                    }
                }
            }
        }

        //aduItems.append(aduInfo);
    }

    result.insert("aduItems", QJsonValue(aduItems));

    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::uploadAduFirmware(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;
    QString firmwareLocalPath;

    FirmwareUpdater::FirmWareInfo stFirmwareInfo;
    stFirmwareInfo.strFirmWareVersion = request.getParameter("version");
    stFirmwareInfo.strFileName = request.getParameter("fileName");
    stFirmwareInfo.iFileSize = QString(request.getParameter("fileSize")).toInt();
    stFirmwareInfo.eFirmwareType = static_cast<FirmwareUpdater::FirmWareType>(QString(request.getParameter("fileType")).toInt());
    stFirmwareInfo.signatureMethod = request.getParameter("signatureMethod");
    stFirmwareInfo.signatureValue = request.getParameter("signatureValue");

    logInfo(QString("upload adu firmware version:%1, fileName:%2, filesize:%3, signatureMethod:%4, signatureValue:%5").
            arg(stFirmwareInfo.strFirmWareVersion).arg(stFirmwareInfo.strFileName).
            arg(stFirmwareInfo.iFileSize).arg(stFirmwareInfo.signatureMethod).arg(stFirmwareInfo.signatureValue));

    QTemporaryFile *uploadFile = request.getUploadedFile(STR_FILE_DATA);
    if ((uploadFile != NULL) && uploadFile->open())
    {
        QByteArray fileData = uploadFile->readAll();

        FirmwareUpdater::FirmwareCheckErrorCode checkRet =  FirmwareUpdater::FirmwareUpdaterManager::CheckFirmware(stFirmwareInfo, fileData);
        switch (checkRet)
        {
        case FirmwareUpdater::FILE_SIGNATUREVALUE_MISMATCH:
            eErrorCode = ERROR_CODE_FIRMWARE_CHECK_ERROR;
            break;
        case FirmwareUpdater::FILE_SIZE_ERROR:
            eErrorCode = ERROR_CODE_FIRMWARE_FILE_SIZE_ERROR;
            break;
        default:
            break;
        }

        if(FirmwareUpdater::FILE_OK == checkRet)
        {
            bool  bSaveFileRet = FirmwareUpdater::FirmwareUpdaterManager::saveAduFirmwareToLocal(stFirmwareInfo.strFileName, fileData, firmwareLocalPath);
            if(!bSaveFileRet)
            {
                eErrorCode = ERROR_CODE_FIRMWARE_SAVE_ERROR;
            }
        }

        uploadFile->close();
    }
    else
    {
        eErrorCode = ERROR_CODE_FIRMWARE_UPLOAD_ERROR;
        logError("upload file error...");
    }

    result.insert("filePath", firmwareLocalPath);

    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::upgradeAduFirmware(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;
    QString upgradeTaskId; //传感器升级任务id

    FirmwareUpdater::UpdateAduFirmwareParams stUpdateParams;
    stUpdateParams.eUpgradeType = ConfigService::instance().getADUTypEnum(request.getParameter(STR_ADU_TYPE));
    stUpdateParams.strFirmwarePath = request.getParameter("filePath");
    stUpdateParams.strUpdateTaskName = request.getParameter("taskName");
    stUpdateParams.upgradeAduIdList = QString(request.getParameter("deviceIds")).split(',');

    bool bRet = MonitorService::instance().firmUpdate(stUpdateParams, upgradeTaskId);
    if(!bRet)
    {
        eErrorCode = ERROR_CODE_FIRMWARE_UPGRADE_ERROR;
    }

    result.insert("upgradeTaskId", upgradeTaskId);
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getUpgradeAduTaskRecord(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;

    int page = request.getParameter("page").toInt();
    int pageSize = request.getParameter("pageSize").toInt();
    //QString strUpgradeTaskId = request.getParameter("upgradeTaskId");


    QList<UpgradeTaskRecord> taskRecordList, listResponData;
    DBServer::instance().getUpgradeTaskRecord(taskRecordList);
    std::sort(taskRecordList.begin(), taskRecordList.end(), [](const UpgradeTaskRecord &a, const UpgradeTaskRecord &b){
       return a.taskCreateTime > b.taskCreateTime;
    });

    //任务数据大小
    const int taskDataSize = taskRecordList.size();

    //总页数
    int pageTotal = taskDataSize % pageSize == 0 ? taskDataSize / pageSize : taskDataSize / pageSize + 1;

    //响应页大小
    const int responseSize = page * pageSize >= taskDataSize ? taskDataSize - (page - 1)* pageSize : pageSize;
    listResponData = taskRecordList.mid((page - 1) * pageSize, responseSize);

    QJsonArray upgradeTaskList;
    int taskDataID = 0;
    for(int i = 0; i < responseSize; ++i)
    {
        QJsonObject taskRecord;
        taskRecord.insert("id", taskDataID++);
        taskRecord.insert("upgradeTaskId", listResponData.at(i).taskId);
        taskRecord.insert("name", listResponData.at(i).taskName);
        taskRecord.insert("firmwareId", listResponData.at(i).FirmwareName);
        taskRecord.insert("productId", listResponData.at(i).deviceType);
        taskRecord.insert("createTime", listResponData.at(i).taskCreateTime.toString(STR_DATE_TIME_QSTRING_CNA));
        //升级状态统计
        int successNum = 0, failNum = 0, waitNum = 0, updatingNum = 0;
        QList<UpgradeTaskDetails> taskDetailsRecordList;
        DBServer::instance().getUpgradeTaskDetailsRecord(listResponData.at(i).taskId, taskDetailsRecordList);

        for(auto iter = taskDetailsRecordList.cbegin(); iter != taskDetailsRecordList.cend(); ++iter)
        {
            FirmwareUpdater::AduUpdateStatus eStatus = static_cast<FirmwareUpdater::AduUpdateStatus>((*iter).upgradeStatus);

            switch (eStatus) {
            case FirmwareUpdater::ADU_WAIT_UPDATE:
                ++waitNum;
                break;
            case FirmwareUpdater::ADU_UPDATING:
                ++updatingNum;
                break;
            case FirmwareUpdater::ADU_UPDATE_SUCCESS:
                ++successNum;
                break;
            case FirmwareUpdater::ADU_UPDATE_FAILED:
                ++failNum;
                break;
            default:
                break;
            }
        }
        taskRecord.insert("successNum", successNum);
        taskRecord.insert("failNum", failNum);
        taskRecord.insert("waitNum", waitNum);
        taskRecord.insert("updating", updatingNum);


        upgradeTaskList.append(taskRecord);
    }

    result.insert("pageNum", page);
    result.insert("pageSize", pageSize);
    result.insert("total", pageTotal);
    result.insert("taskData", upgradeTaskList);
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getUpgradeAduTaskDetails(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;

    int page = request.getParameter("page").toInt();
    int pageSize = request.getParameter("pageSize").toInt();
    QString strUpgradeTaskId = request.getParameter("upgradeTaskId");

    QList<UpgradeTaskDetails> taskDetailsRecordList, listResponData;
    DBServer::instance().getUpgradeTaskDetailsRecord(strUpgradeTaskId, taskDetailsRecordList);

    //任务数据大小
    const int taskDataSize = taskDetailsRecordList.size();

    //总页数
    int pageTotal = taskDataSize % pageSize == 0 ? taskDataSize / pageSize : taskDataSize / pageSize + 1;

    //响应页大小
    const int responseSize = page * pageSize >= taskDataSize ? taskDataSize - (page - 1)* pageSize : pageSize;
    listResponData = taskDetailsRecordList.mid((page - 1) * pageSize, responseSize);

    QJsonArray upgradeTaskDetailsList;
    int taskDataID = 0;
    for(int i = 0; i < responseSize; ++i)
    {
        QJsonObject taskDetailsRecord;
        taskDetailsRecord.insert("id", taskDataID++);
        taskDetailsRecord.insert("upgradeState", listResponData.at(i).upgradeStatus);
        taskDetailsRecord.insert("deviceId", listResponData.at(i).deviceName);
        //获取传感器名称
        ADUUnitInfo aduUnitInfo;
        ConfigService::instance().findADU(listResponData.at(i).deviceName, aduUnitInfo);
        const QString strAduName = aduUnitInfo.strName;
        taskDetailsRecord.insert("deviceName", strAduName);

        //任务信息查询
        QList<UpgradeTaskRecord> taskRecordList;
        DBServer::instance().getUpgradeTaskRecord(taskRecordList, listResponData.at(i).taskID);
        if(!taskRecordList.isEmpty())
        {
            taskDetailsRecord.insert("firmwareId", taskRecordList.first().FirmwareName);
            taskDetailsRecord.insert("taskName", taskRecordList.first().taskName);
            taskDetailsRecord.insert("productId", taskRecordList.first().deviceType);
        }
        taskDetailsRecord.insert("upgradeTaskId", listResponData.at(i).taskID);

        //taskDetailsRecord.insert("createTime", listResponData.at(i).taskCreateTime.toString(STR_DATE_TIME_QSTRING_CNA));

        upgradeTaskDetailsList.append(taskDetailsRecord);
    }

    result.insert("pageNum", page);
    result.insert("pageSize", pageSize);
    result.insert("total", pageTotal);
    result.insert("taskDetailsData", upgradeTaskDetailsList);
    result.insert("firmwareSavePath", FirmwareUpdater::FirmwareUpdaterManager::getAduFirmwareSavePath());
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getUpgradeAduTaskLog(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QString strTaskId = request.getParameter("upgradeTaskId");
    QString strAduId = request.getParameter("deviceId");
    QJsonObject result;
    QString strLogData; //升级日志文件数据

    bool bRet = FirmwareUpdater::FirmwareUpdaterManager::readUpgradeAduLogFromFile(strTaskId, strAduId, strLogData);

    result.insert("logFileData", QJsonValue(strLogData));
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getUpgradeTaskStatus(HttpRequest &request, HttpResponse &response)
{
    Q_UNUSED(request)

    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;
    const int currentUpgradeTaskSize =  MonitorService::instance().getUpdateFirmwareTaskStatus();
    const int upgradeTaskMaxSize = FirmwareUpdater::FirmwareUpdaterManager::getUpgradeTaskMaxSize();

    bool isFull = currentUpgradeTaskSize >= upgradeTaskMaxSize ? true : false;
    result.insert("isFull", isFull);
    result.insert("taskMaxSize", upgradeTaskMaxSize);
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

void CommandServiceForApp::getAduFirmwareFileStatus(HttpRequest &request, HttpResponse &response)
{
    HTTPErrorCode eErrorCode = ERROR_CODE_NONE_ERROR;
    QJsonObject result;

    FirmwareUpdater::FirmWareInfo stFirmwareInfo;
    stFirmwareInfo.strFileName = request.getParameter("fileName");
    stFirmwareInfo.iFileSize = QString(request.getParameter("fileSize")).toInt();
    stFirmwareInfo.signatureMethod = request.getParameter("signatureMethod");
    stFirmwareInfo.signatureValue = request.getParameter("signatureValue");

    QString strFirmwareLocalPath;
    FirmwareUpdater::FirmwareCheckErrorCode checkRet = FirmwareUpdater::FirmwareUpdaterManager::checkAduFirmwareFileStatus(stFirmwareInfo, strFirmwareLocalPath);
    bool isFileExist = FirmwareUpdater::FILE_OK == checkRet ? true : false;

    result.insert("isFileExist", isFileExist);
    result.insert("filePath", strFirmwareLocalPath);
    responseWrite(QJsonDocument(encapsulationData(result, eErrorCode)).toJson())
}

/*************************************************
说明：
*************************************************************/
void CommandServiceForApp::onSampleCommand(const QString&  strID)
{
    if(strID.isEmpty())
    {
        ConfigService &configService = ConfigService::instance();
        const QList<ADUUnitInfo> &adus =  configService.ADUList();

        foreach(ADUUnitInfo tmpInfo,adus)
        {
            MonitorService::instance().sample(tmpInfo.strID, tmpInfo.eType) ;
        }
    }
    else
    {
        ADUType eADUType;
        ConfigService::instance().getADUTypeFromID(strID, eADUType);
        MonitorService::instance().sample(strID, eADUType);
    }
}

/*************************************************
说明：
*************************************************************/
void CommandServiceForApp::onAddADUInfoApp(QMap<QString, QString> items)
{
    ADUType eADUType = (ADUType)items.value("eType").toInt();
    if(eADUType == ADU_TEMP_HUM_IS)  //温湿度传感器
    {
        QString strADUID = items.value("strID");
        QString strADUOldID = items.value("strADUOldID");

        ADUUnitInfo adu;

        ConfigService::instance().getADU(strADUID, eADUType, adu);
        adu.strID = strADUID;
        adu.eType = eADUType;
        adu.strName = items.value("strName");
        adu.eLinkGroup = (Monitor::LinkGroup)items.value("eLinkGroup").toInt();
        adu.strRS485ComPort = items.value("strRS485ComPort");
        adu.iTaskGroup = items.value("strName").toInt();

        adu.stADUParam.ucFrequency = items.value("ucFrequency").toInt();
        adu.stADUParam.ucWorkGroup = items.value("ucWorkGroup").toInt();
        adu.stADUParam.ucConnectionLoad = items.value("ucConnectionLoad").toInt();
        adu.stADUParam.ucConnectionSpeed = items.value("ucConnectionSpeed").toInt();
        adu.stADUParam.uiSleepTime = items.value("uiSleepTime").toInt();
        adu.stADUParam.uiSampleSpace = items.value("uiSampleSpace").toInt();
        adu.stADUParam.usStartSampleTime = items.value("usStartSampleTime").toInt();
        adu.stADUParam.ucAutoUpdate = items.value("ucAutoUpdate").toInt();
        adu.stADUParam.eADUWorkModel = (Monitor::ADUWorkMode)items.value("eADUWorkModel").toInt();
        adu.stADUParam.uiAutoGivingSpace = items.value("uiAutoGivingSpace").toInt();
        adu.stADUParam.eLinkGroup = adu.eLinkGroup;
        if ( Monitor::WORKER_MODEL_LOWPOWER == adu.stADUParam.eADUWorkModel )
        {
            adu.stADUParam.ucStartArtificialTime = 8;
            adu.stADUParam.ucEndArtificialTime = 20;
            adu.stADUParam.usArtificialWakeUpInterval = 3;//request.getParameter(STR_ADU_ARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
            adu.stADUParam.usNotArtificialWalkUpInterval = 30;//request.getParameter(STR_ADU_UNARTIFICIAL_STATE_WAKE_UP_SPACE).toInt();
            adu.stADUParam.bAutoChangeMode = 1;//request.getParameter(STR_ADU_IS_AUTO_CHANGE_MODE).toInt();
        }
    #ifndef Q_WS_QWS
        ConfigService::instance().saveADUInfo(adu);
    #endif
        ConfigService::instance().saveADUName(adu.strID, adu.strName);

        //前端存在，此时为修改前端
        if(ConfigService::instance().isADUIDExisted(strADUOldID))
        {
            if(strADUOldID != strADUID)  //更改了前端ID
            {
                ConfigService::instance().setAduID(strADUOldID, strADUID);
            }
        }
        else //前端不存在，按照默认参数本地存储,防止后续操作找不到前端
        {
            ADUUnitInfo defaultAdu = adu;
            defaultAdu.stADUParam.uiSleepTime = 1;
            defaultAdu.stADUParam.eADUWorkModel = Monitor::WORKER_MODEL_MAINTAIN;
            ConfigService::instance().addADUInfo(defaultAdu);
            adu.stADUParam.usNumInGroup = defaultAdu.stADUParam.usNumInGroup;
        }

        MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam);

        return;
    }
    else
    {
        QString strADUID = items.value("strID");
        QString strADUOldID = items.value("strADUOldID");

        ADUUnitInfo adu;

        ConfigService::instance().setAduID(strADUOldID, strADUID);
        bool bIsExist = ConfigService::instance().getADU(strADUID, eADUType, adu);
        adu.strID = strADUID;
        adu.eType = eADUType;
        adu.strName = items.value("strName");
        adu.eLinkGroup = (Monitor::LinkGroup)items.value("eLinkGroup").toInt();
        adu.strRS485ComPort = items.value("strRS485ComPort");
        adu.iTaskGroup = items.value("strName").toInt();

        adu.stADUParam.ucFrequency = items.value("ucFrequency").toInt();
        adu.stADUParam.ucWorkGroup = items.value("ucWorkGroup").toInt();
        adu.stADUParam.ucConnectionLoad = items.value("ucConnectionLoad").toInt();
        adu.stADUParam.ucConnectionSpeed = items.value("ucConnectionSpeed").toInt();
        adu.stADUParam.uiSleepTime = items.value("uiSleepTime").toInt();
        adu.stADUParam.uiSampleSpace = items.value("uiSampleSpace").toInt();
        adu.stADUParam.usStartSampleTime = items.value("usStartSampleTime").toInt();
        adu.stADUParam.ucAutoUpdate = items.value("ucAutoUpdate").toInt();
        adu.stADUParam.eADUWorkModel = (Monitor::ADUWorkMode)items.value("eADUWorkModel").toInt();
        adu.stADUParam.uiAutoGivingSpace = items.value("uiAutoGivingSpace").toInt();
        adu.stADUParam.eLinkGroup = adu.eLinkGroup;
        adu.stADUParam.extendInfo = items.value("extendInfo");

        if ( Monitor::WORKER_MODEL_LOWPOWER == adu.stADUParam.eADUWorkModel )
        {
            adu.stADUParam.ucStartArtificialTime = 8;
            adu.stADUParam.ucEndArtificialTime = 20;
            adu.stADUParam.usArtificialWakeUpInterval = 3;
            adu.stADUParam.usNotArtificialWalkUpInterval = 30;
            adu.stADUParam.bAutoChangeMode = 1;
        }

        ConfigService::instance().saveADUName( adu.strID, adu.strName);

        if (!bIsExist)   //前端不存在, 添加传感器
        {
            // 保存前端名及链路信息
            adu.stADUParam.uiSleepTime = 1;
            ConfigService::instance().addADUInfo(adu);
            if(MoistureController::isMoistureAdu(adu.eType))
            {
                MoistureController::instance().addTransmitter(adu);
            }
        }

        if (ConfigService::instance().getISADUType(adu.eType))
        {
            if(!bIsExist)
            {
                adu.stADUParam.uiSleepTime  = items.value("uiSleepTime").toInt();
            }
            MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam);
        }
        else
        {
            ConfigService::instance().saveADUInfo(adu);
            if(MoistureController::isMoistureAdu(adu.eType))
            {
                MoistureController::instance().updateTransmitterParam(strADUID, adu);
            }
        }
    }
}

void CommandServiceForApp::onSaveADUInfoAndChannelInfoApp(QMap<QString, QString> items)
{
    ADUType eADUType = (ADUType)items.value("eType").toInt();

    QString strADUID = items.value("strID");
    QString strADUOldID = items.value("strADUOldID");

    ADUUnitInfo adu;

    ConfigService::instance().setAduID(strADUOldID, strADUID);
    bool bIsExist = ConfigService::instance().getADU(strADUID, eADUType, adu);


    ADUChannelInfo channel;
    channel.etype =  (ADUChannelType)items.value("eChannelType").toInt();
    channel.unID = items.value("unID").toInt();
    channel.strName = items.value("strChannelName");

    bool  bChannelParam = false;
    switch (channel.etype)
    {
    case CHANNEL_AE://AE参数
    {
        channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
        channel.stachpara.staAEPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
        channel.stachpara.staAEPara.ucSampleCycles = 5;
        channel.stachpara.staAEPara.usSampelCount = 100;
        bChannelParam = true;
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        channel.stachpara.staTEVPara.cBackGroundNum = items.value("cBackGroundNum").toInt();
    }
        break;
    case CHANNEL_UHF:
    {
        channel.stachpara.staUHFPara.cGain = items.value("cGain").toInt();
        channel.stachpara.staUHFPara.cBandWidth = (UHFDataFilter)items.value("cBandWidth").toInt();
        channel.stachpara.staUHFPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
        channel.stachpara.staUHFPara.ucSampleCycles = 50;
        channel.stachpara.staUHFPara.usSampelCount = 60;
        bChannelParam = true;
    }
        break;
    case CHANNEL_HFCT:
    {
        channel.stachpara.staHFCTPara.cGain = items.value("cGain").toInt();
        channel.stachpara.staHFCTPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
        channel.stachpara.staHFCTPara.ucSampleCycles = 50;
        channel.stachpara.staHFCTPara.usSampelCount = 60;
        bChannelParam = true;
    }
        break;
    case CHANNEL_MECH:
    {
        channel.stachpara.staMechPara.usLoopCurrentThred = items.value("usLoopCurrentThred").toInt();
        channel.stachpara.staMechPara.usMotorCurrentThred = items.value("usMotorCurrentThred").toInt();
        channel.stachpara.staMechPara.bSwitchState = items.value("bSwitchState").toInt();
        channel.stachpara.staMechPara.bBreakerType = items.value("bBreakerType").toInt();
        channel.stachpara.staMechPara.bMotorFunctionType = items.value("bMotorFunctionType").toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        channel.stachpara.staArresterIPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        channel.stachpara.staArresterUPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
        channel.stachpara.staArresterUPara.fTransformationRatio = items.value("fTransformationRatio").toFloat();
    }
        break;
    case CHANNEL_VIBRATION:
    {
        channel.stachpara.staVibrationParam.ucSampleCycles = items.value("ucSampleCycles").toInt();
        channel.stachpara.staVibrationParam.usSampelCount = items.value("usSampelCount").toInt();
        bChannelParam = true;
    }
        break;
    case CHANNEL_SAW:
    {
    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staTempParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
        channel.stachpara.staTempParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
        channel.stachpara.staTempParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
        channel.unID = 0;
        bChannelParam = true;
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staHumParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
        channel.stachpara.staHumParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
        channel.stachpara.staHumParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
        channel.unID = 1;
        bChannelParam = true;
    }
        break;
    case CHANNEL_FLOOD:
    {
        channel.stachpara.stFloodChannelPara.cFlood = items.value("cFlood").toInt();
        channel.unID = 0;

    }
        break;
    default:
        break;
    }

    if (bChannelParam)
    {
        ConfigService::instance().saveChannelName(strADUID, channel, channel.strName);
    }

    adu.strID = strADUID;
    adu.eType = eADUType;
    adu.strName = items.value("strName");
    adu.eLinkGroup = (Monitor::LinkGroup)items.value("eLinkGroup").toInt();
    adu.strRS485ComPort = items.value("strRS485ComPort");
    adu.iTaskGroup = items.value("strName").toInt();

    adu.stADUParam.ucFrequency = items.value("ucFrequency").toInt();
    adu.stADUParam.ucWorkGroup = items.value("ucWorkGroup").toInt();
    adu.stADUParam.ucConnectionLoad = items.value("ucConnectionLoad").toInt();
    adu.stADUParam.ucConnectionSpeed = items.value("ucConnectionSpeed").toInt();
    adu.stADUParam.uiSleepTime = items.value("uiSleepTime").toInt();
    adu.stADUParam.uiSampleSpace = items.value("uiSampleSpace").toInt();
    adu.stADUParam.usStartSampleTime = items.value("usStartSampleTime").toInt();
    adu.stADUParam.ucAutoUpdate = items.value("ucAutoUpdate").toInt();
    adu.stADUParam.eADUWorkModel = (Monitor::ADUWorkMode)items.value("eADUWorkModel").toInt();
    adu.stADUParam.uiAutoGivingSpace = items.value("uiAutoGivingSpace").toInt();
    adu.stADUParam.eLinkGroup = adu.eLinkGroup;
    adu.stADUParam.extendInfo = items.value("extendInfo");

    if ( Monitor::WORKER_MODEL_LOWPOWER == adu.stADUParam.eADUWorkModel )
    {
        adu.stADUParam.ucStartArtificialTime = 8;
        adu.stADUParam.ucEndArtificialTime = 20;
        adu.stADUParam.usArtificialWakeUpInterval = 3;
        adu.stADUParam.usNotArtificialWalkUpInterval = 30;
        adu.stADUParam.bAutoChangeMode = 1;
    }

    ConfigService::instance().saveADUName( adu.strID, adu.strName);

    if (!bIsExist)   //前端不存在, 添加传感器
    {
        // 保存前端名及链路信息
        adu.stADUParam.uiSleepTime = 1;
        ConfigService::instance().addADUInfo(adu);
        if(MoistureController::isMoistureAdu(adu.eType))
        {
            MoistureController::instance().addTransmitter(adu);
        }
    }

    if (ConfigService::instance().getISADUType(adu.eType))
    {
        if(!bIsExist)
        {
            adu.stADUParam.uiSleepTime  = items.value("uiSleepTime").toInt();
        }
        MonitorService::instance().setADUInfo(strADUID, adu.eType, adu.stADUParam, channel.stachpara);
    }
    else
    {
        ConfigService::instance().saveADUInfo(adu);
        if(MoistureController::isMoistureAdu(adu.eType))
        {
            MoistureController::instance().updateTransmitterParam(strADUID, adu);
        }
    }

}


/*************************************************
说明：
*************************************************************/
void CommandServiceForApp::onApplyADUConfigAndSensorConfigApp(QMap<QString, QString> items)
{
    ADUUnitInfo staADUInfo;
    ADUParam stADUParam;
    ADUChannelInfo channel;
    channel.etype = (ADUChannelType)items.value("eChannelType").toInt();
    ADUType eADUType = (ADUType)items.value("eType").toInt();
    switch (channel.etype)
    {
    case CHANNEL_AE://AE参数
    {
        channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
        channel.stachpara.staAEPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
        channel.stachpara.staAEPara.ucSampleCycles = items.value("ucSampleCycles").toInt();
        channel.stachpara.staAEPara.usSampelCount = items.value("usSampelCount").toInt();
        channel.unID = 0;//TODO 协议中无该索引值，目前只能手动填充
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        channel.stachpara.staTEVPara.cBackGroundNum = items.value("cBackGroundNum").toInt();
        channel.unID = 1;//TODO 协议中无该索引值，目前只能手动填充
    }
        break;
    case CHANNEL_UHF:
    {
        channel.stachpara.staUHFPara.cGain = items.value("cGain").toInt();
        channel.stachpara.staUHFPara.cBandWidth = (UHFDataFilter)items.value("cBandWidth").toInt();
        channel.stachpara.staUHFPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
        channel.stachpara.staUHFPara.ucSampleCycles = items.value("ucSampleCycles").toInt();
        channel.stachpara.staUHFPara.usSampelCount = 60;
    }
        break;
    case CHANNEL_HFCT:
    {
        channel.stachpara.staHFCTPara.cGain = items.value("cGain").toInt();
        channel.stachpara.staHFCTPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
        channel.stachpara.staHFCTPara.ucSampleCycles = items.value("ucSampleCycles").toInt();
        channel.stachpara.staHFCTPara.usSampelCount = 60;
    }
        break;
    case CHANNEL_MECH:
    {
        channel.stachpara.staMechPara.usLoopCurrentThred = items.value("usLoopCurrentThred").toInt();
        channel.stachpara.staMechPara.usMotorCurrentThred = items.value("usMotorCurrentThred").toInt();
        channel.stachpara.staMechPara.bSwitchState = items.value("bSwitchState").toInt();
        channel.stachpara.staMechPara.bBreakerType = items.value("bBreakerType").toInt();
        channel.stachpara.staMechPara.bMotorFunctionType = items.value("bMotorFunctionType").toInt();
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        channel.stachpara.staArresterIPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
    }
        break;
    case CHANNEL_ARRESTER_U:
    {
        channel.stachpara.staArresterUPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
        channel.stachpara.staArresterUPara.fTransformationRatio = items.value("fTransformationRatio").toFloat();
    }
        break;
    case CHANNEL_VIBRATION:
    {
        channel.stachpara.staVibrationParam.ucSampleCycles = items.value("ucSampleCycles").toInt();
        channel.stachpara.staVibrationParam.usSampelCount = items.value("usSampelCount").toInt();
    }
        break;
    case CHANNEL_SAW:
    {

    }
        break;
    case CHANNEL_AX8:
    {
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staTempParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
        channel.stachpara.staTempParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
        channel.stachpara.staTempParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
        channel.unID = 0;
    }
        break;
    case CHANNEL_FLOOD:
    {
        channel.stachpara.stFloodChannelPara.cFlood = items.value("cFlood").toFloat();
        channel.unID = 0;
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        //TODO 温湿度智能传感器
        channel.stachpara.staHumParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
        channel.stachpara.staHumParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
        channel.stachpara.staHumParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
        channel.unID = 1;
    }
        break;
    default:
    {
        PDS_SYS_WARNING_LOG("Unsupport channel type %d", channel.etype);
        break;
    }
    }

    if (eADUType == ADU_TEMP_HUM_IS || eADUType == ADU_FLOOD_IS)
    {
        ConfigService::instance().getADUConfig(eADUType, staADUInfo);
        stADUParam = staADUInfo.stADUParam;
        stADUParam.uiAutoGivingSpace = items.value("uiAutoGivingSpace").toInt();
    }

    stADUParam.ucFrequency = items.value("ucFrequency").toInt();
    stADUParam.ucWorkGroup = items.value("ucWorkGroup").toInt();
    stADUParam.ucConnectionLoad = items.value("ucConnectionLoad").toInt();
    stADUParam.ucConnectionSpeed = items.value("ucConnectionSpeed").toInt();
    stADUParam.uiSleepTime = items.value("uiSleepTime").toInt();
    stADUParam.uiSampleSpace = items.value("uiSampleSpace").toInt();
    stADUParam.usStartSampleTime = items.value("usStartSampleTime").toInt();
    stADUParam.ucAutoUpdate = items.value("ucAutoUpdate").toInt();
    stADUParam.eADUWorkModel = (Monitor::ADUWorkMode)items.value("eADUWorkModel").toInt();
    stADUParam.extendInfo =  items.value("extendInfo");
    if ( Monitor::WORKER_MODEL_LOWPOWER == stADUParam.eADUWorkModel )
    {
        stADUParam.ucStartArtificialTime = items.value("ucStartArtificialTime").toInt();
        stADUParam.ucEndArtificialTime = items.value("ucEndArtificialTime").toInt();
        stADUParam.usArtificialWakeUpInterval = items.value("usArtificialWakeUpInterval").toInt();
        stADUParam.usNotArtificialWalkUpInterval = items.value("usNotArtificialWalkUpInterval").toInt();
        stADUParam.bAutoChangeMode = items.value("bAutoChangeMode").toInt();
    }

    ConfigService &config = ConfigService::instance();
    if (eADUType == ADU_PD_THREE || eADUType == ADU_PD_FIVE)
    {
        QString strChannelName = items.value("strChannelName");
        if(strChannelName == "TEV")
        {
            ADUChannelInfo channel;
            channel.etype = (ADUChannelType)items.value("eChannelType").toInt();
            channel.unID = items.value("unID").toInt();
            channel.strName = items.value("strName").toInt();
            channel.stachpara.staTEVPara.cBackGroundNum = items.value("cBackGroundNum").toInt();

            QStringList listADUID = ConfigService::instance().ADUID(eADUType);
            for (int i = 0; i < listADUID.size(); i++)
            {
                ConfigService::instance().saveChannelPara(channel.stachpara, listADUID.at(i), 1);
            }
        }
        if(strChannelName == "AE")
        {
            channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
            QStringList listADUID = ConfigService::instance().ADUID(eADUType);
            for (int i = 0; i < listADUID.size(); i++)
            {
                MonitorService::instance().setChannelInfo(listADUID.at(i), eADUType, channel.unID, channel.stachpara);
                ConfigService::instance().saveChannelPara( channel.stachpara, listADUID.at(i), 0 );
            }
        }
    }
    else if(config.getISADUType(eADUType))
    {
        if( (eADUType == ADU_PD_IS) && (channel.etype == CHANNEL_TEV) )    //TEV通道需要特殊处理
        {
            //TODO TEV参数只需保存在主机上
            QList<ADUUnitInfo> aduPdis = ConfigService::instance().ADUList(eADUType);
            foreach(ADUUnitInfo aduInfo, aduPdis)
            {
                ConfigService::instance().saveChannelPara(channel.stachpara, aduInfo.strID, channel.unID);
                MonitorService::instance().setADUInfo("", eADUType, stADUParam, channel.stachpara);
            }
        }
        else
        {
            MonitorService::instance().setADUInfo("", eADUType, stADUParam, channel.stachpara);
        }
    }
    else if(MoistureController::instance().isMoistureAdu(eADUType))
    {
        QStringList adus = config.ADUID(eADUType);
        ADUUnitInfo aduinfo;
        for(int i = 0; i < adus.size(); ++i)
        {
            if(config.getADU(adus[i], aduinfo))
            {
                aduinfo.stADUParam = stADUParam;
                if(CONFIG_NO_ERROR == config.saveADUInfo(aduinfo))
                {
                    MoistureController::instance().updateTransmitterParam(adus[i], aduinfo);
                }
            }
        }
    }
}


/*************************************************
说明：
*************************************************************/
//void CommandServiceForApp::onApplyADUConfigApp(QMap<QString, QString> items)
//{
//    ADUUnitInfo staADUInfo;
//    ADUParam stADUParam;

//    ADUType eADUType = (ADUType)items.value("eType").toInt();
//    if (eADUType == ADU_TEMP_HUM_IS || eADUType == ADU_FLOOD_IS)
//    {
//        ConfigService::instance().getADUConfig(eADUType, staADUInfo);
//        stADUParam = staADUInfo.stADUParam;
//        stADUParam.uiAutoGivingSpace = items.value("uiAutoGivingSpace").toInt();
//    }

//    stADUParam.ucFrequency = items.value("ucFrequency").toInt();
//    stADUParam.ucWorkGroup = items.value("ucWorkGroup").toInt();
//    stADUParam.ucConnectionLoad = items.value("ucConnectionLoad").toInt();
//    stADUParam.ucConnectionSpeed = items.value("ucConnectionSpeed").toInt();
//    stADUParam.uiSleepTime = items.value("uiSleepTime").toInt();
//    stADUParam.uiSampleSpace = items.value("uiSampleSpace").toInt();
//    stADUParam.usStartSampleTime = items.value("usStartSampleTime").toInt();
//    stADUParam.ucAutoUpdate = items.value("ucAutoUpdate").toInt();
//    stADUParam.eADUWorkModel = (Monitor::ADUWorkMode)items.value("eADUWorkModel").toInt();
//    stADUParam.extendInfo =  items.value("extendInfo");
//    if ( Monitor::WORKER_MODEL_LOWPOWER == stADUParam.eADUWorkModel )
//    {
//        stADUParam.ucStartArtificialTime = items.value("ucStartArtificialTime").toInt();
//        stADUParam.ucEndArtificialTime = items.value("ucEndArtificialTime").toInt();
//        stADUParam.usArtificialWakeUpInterval = items.value("usArtificialWakeUpInterval").toInt();
//        stADUParam.usNotArtificialWalkUpInterval = items.value("usNotArtificialWalkUpInterval").toInt();
//        stADUParam.bAutoChangeMode = items.value("bAutoChangeMode").toInt();
//    }

//    ConfigService &config = ConfigService::instance();
//    if (config.getISADUType(eADUType))
//    {
//        QString strADUID;
//        MonitorService::instance().setADUInfo(strADUID, eADUType, stADUParam);
//    }
//    else if(MoistureController::instance().isMoistureAdu(eADUType))
//    {
//        QStringList adus = config.ADUID(eADUType);
//        ADUUnitInfo aduinfo;
//        for(int i = 0; i < adus.size(); ++i)
//        {
//            if(config.getADU(adus[i], aduinfo))
//            {
//                aduinfo.stADUParam = stADUParam;
//                if(CONFIG_NO_ERROR == config.saveADUInfo(aduinfo))
//                {
//                    MoistureController::instance().updateTransmitterParam(adus[i], aduinfo);
//                }
//            }
//        }
//    }
//}

//void CommandServiceForApp::onApplySensorConfigApp(QMap<QString, QString> items)
//{
//    ADUChannelInfo channel;
//    channel.etype = (ADUChannelType)items.value("etype").toInt();
//    ADUType eADUType =  (ADUType)items.value("eADUType").toInt();

//    switch (channel.etype)
//    {
//    case CHANNEL_AE://AE参数
//    {
//        channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
//        channel.stachpara.staAEPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
//        channel.stachpara.staAEPara.ucSampleCycles = items.value("ucSampleCycles").toInt();
//        channel.stachpara.staAEPara.usSampelCount = items.value("usSampelCount").toInt();
//        channel.unID = 0;//TODO 协议中无该索引值，目前只能手动填充
//    }
//        break;
//    case CHANNEL_TEV://TEV参数
//    {
//        channel.stachpara.staTEVPara.cBackGroundNum = items.value("cBackGroundNum").toInt();
//        channel.unID = 1;//TODO 协议中无该索引值，目前只能手动填充
//    }
//        break;
//    case CHANNEL_UHF:
//    {
//        channel.stachpara.staUHFPara.cGain = items.value("cGain").toInt();
//        channel.stachpara.staUHFPara.cBandWidth = (UHFDataFilter)items.value("cBandWidth").toInt();
//        channel.stachpara.staUHFPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
//        channel.stachpara.staUHFPara.ucSampleCycles = items.value("ucSampleCycles").toInt();
//        channel.stachpara.staUHFPara.usSampelCount = 60;

//    }
//        break;
//    case CHANNEL_HFCT:
//    {
//        channel.stachpara.staHFCTPara.cGain = items.value("cGain").toInt();
//        channel.stachpara.staHFCTPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
//        channel.stachpara.staHFCTPara.ucSampleCycles = items.value("ucSampleCycles").toInt();
//        channel.stachpara.staHFCTPara.usSampelCount = 60;

//    }
//        break;
//    case CHANNEL_MECH:
//    {
//        channel.stachpara.staMechPara.usLoopCurrentThred = items.value("usLoopCurrentThred").toInt();
//        channel.stachpara.staMechPara.usMotorCurrentThred = items.value("usMotorCurrentThred").toInt();
//        channel.stachpara.staMechPara.bSwitchState = items.value("bSwitchState").toInt();
//        channel.stachpara.staMechPara.bBreakerType = items.value("bBreakerType").toInt();
//        channel.stachpara.staMechPara.bMotorFunctionType = items.value("bMotorFunctionType").toInt();
//    }
//        break;
//    case CHANNEL_ARRESTER_I:
//    case CHANNEL_GROUNDDINGCURRENT:
//    case CHANNEL_LEAKAGECURRENT:
//    {
//        channel.stachpara.staArresterIPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
//    }
//        break;
//    case CHANNEL_ARRESTER_U:
//    {
//        channel.stachpara.staArresterUPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
//        channel.stachpara.staArresterUPara.fTransformationRatio = items.value("fTransformationRatio").toFloat();
//    }
//        break;
//    case CHANNEL_VIBRATION:
//    {
//        channel.stachpara.staVibrationParam.ucSampleCycles = items.value("ucSampleCycles").toInt();
//        channel.stachpara.staVibrationParam.usSampelCount = items.value("usSampelCount").toInt();
//    }
//        break;
//    case CHANNEL_SAW:
//    {

//    }
//        break;
//    case CHANNEL_AX8:
//    {
//    }
//        break;
//    case CHANNEL_TEMPERATURE:
//    {
//        //TODO 温湿度智能传感器
//        channel.stachpara.staTempParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
//        channel.stachpara.staTempParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
//        channel.stachpara.staTempParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
//        channel.unID = 0;

//    }
//        break;
//    case CHANNEL_FLOOD:
//    {
//        channel.stachpara.stFloodChannelPara.cFlood = items.value("cFlood").toFloat();
//        channel.unID = 0;

//    }
//        break;
//    case CHANNEL_HUMIDITY:
//    {
//        //TODO 温湿度智能传感器
//        channel.stachpara.staHumParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
//        channel.stachpara.staHumParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
//        channel.stachpara.staHumParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
//        channel.unID = 1;

//    }
//        break;
//    default:
//    {
//        PDS_SYS_WARNING_LOG("Unsupport channel type %d", channel.etype);
//        break;
//    }
//    }
//    if (eADUType == ADU_PD_THREE || eADUType == ADU_PD_FIVE)
//    {
//        QString strChannelName = items.value("strChannelName");
//        if(strChannelName == "TEV")
//        {
//            ADUChannelInfo channel;
//            channel.etype = (ADUChannelType)items.value("etype").toInt();
//            channel.unID = items.value("unID").toInt();
//            channel.strName = items.value("strName").toInt();
//            channel.stachpara.staTEVPara.cBackGroundNum = items.value("cBackGroundNum").toInt();

//            QStringList listADUID = ConfigService::instance().ADUID(eADUType);
//            for (int i = 0; i < listADUID.size(); i++)
//            {
//                ConfigService::instance().saveChannelPara(channel.stachpara, listADUID.at(i), 1);
//            }
//        }
//        if(strChannelName == "AE")
//        {
//            channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
//            QStringList listADUID = ConfigService::instance().ADUID(eADUType);
//            for (int i = 0; i < listADUID.size(); i++)
//            {
//                MonitorService::instance().setChannelInfo(listADUID.at(i), eADUType, channel.unID, channel.stachpara);
//                ConfigService::instance().saveChannelPara( channel.stachpara, listADUID.at(i), 0 );
//            }
//        }
//    }
//    else if (eADUType == ADU_TEMP_HUM_IS)
//    {
//        MonitorService::instance().setChannelInfo("", eADUType, channel.unID, channel.stachpara);
//    }
//    else
//    {
//        if( (eADUType == ADU_PD_IS) && (channel.etype == CHANNEL_TEV) )    //TEV通道需要特殊处理
//        {
//            //TODO TEV参数只需保存在主机上
//            QList<ADUUnitInfo> aduPdis = ConfigService::instance().ADUList(eADUType);
//            foreach(ADUUnitInfo aduInfo, aduPdis)
//            {
//                ConfigService::instance().saveChannelPara(channel.stachpara, aduInfo.strID, channel.unID);
//            }
//        }
//        else
//        {
//            MonitorService::instance().setChannelInfo("", eADUType, channel.unID, channel.stachpara);
//        }
//    } 
//}

//void CommandServiceForApp::onSaveChannelInfoApp(QMap<QString, QString> items)
//{
//    QString strADUID = items.value("strID");
//    ADUChannelInfo channel;
//    channel.etype =  (ADUChannelType)items.value("eChannelType").toInt();
//    channel.unID = items.value("unID").toInt();
//    channel.strName = items.value("strChannelName");
//    ADUType eADUType;
//    bool  bChannelParam = false;
//    switch (channel.etype)
//    {
//    case CHANNEL_AE://AE参数
//    {
//        channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
//        channel.stachpara.staAEPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
//        channel.stachpara.staAEPara.ucSampleCycles = 5;
//        channel.stachpara.staAEPara.usSampelCount = 100;
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_TEV://TEV参数
//    {
//        channel.stachpara.staTEVPara.cBackGroundNum = items.value("cBackGroundNum").toInt();
//    }
//        break;
//    case CHANNEL_UHF:
//    {
//        channel.stachpara.staUHFPara.cGain = items.value("cGain").toInt();
//        channel.stachpara.staUHFPara.cBandWidth = (UHFDataFilter)items.value("cBandWidth").toInt();
//        channel.stachpara.staUHFPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
//        channel.stachpara.staUHFPara.ucSampleCycles = 50;
//        channel.stachpara.staUHFPara.usSampelCount = 60;
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_HFCT:
//    {
//        channel.stachpara.staHFCTPara.cGain = items.value("cGain").toInt();
//        channel.stachpara.staHFCTPara.eChannelGainType = (ChannelGainType)items.value("eChannelGainType").toInt();
//        channel.stachpara.staHFCTPara.ucSampleCycles = 50;
//        channel.stachpara.staHFCTPara.usSampelCount = 60;
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_MECH:
//    {
//        channel.stachpara.staMechPara.usLoopCurrentThred = items.value("usLoopCurrentThred").toInt();
//        channel.stachpara.staMechPara.usMotorCurrentThred = items.value("usMotorCurrentThred").toInt();
//        channel.stachpara.staMechPara.bSwitchState = items.value("bSwitchState").toInt();
//        channel.stachpara.staMechPara.bBreakerType = items.value("bBreakerType").toInt();
//        channel.stachpara.staMechPara.bMotorFunctionType = items.value("bMotorFunctionType").toInt();
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_ARRESTER_I:
//    case CHANNEL_GROUNDDINGCURRENT:
//    case CHANNEL_LEAKAGECURRENT:
//    {
//        channel.stachpara.staArresterIPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
//    }
//        break;
//    case CHANNEL_ARRESTER_U:
//    {
//        channel.stachpara.staArresterUPara.eChannelPhase = (ChannelPhase)items.value("eChannelPhase").toInt();
//        channel.stachpara.staArresterUPara.fTransformationRatio = items.value("fTransformationRatio").toFloat();
//    }
//        break;
//    case CHANNEL_VIBRATION:
//    {
//        channel.stachpara.staVibrationParam.ucSampleCycles = items.value("ucSampleCycles").toInt();
//        channel.stachpara.staVibrationParam.usSampelCount = items.value("usSampelCount").toInt();
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_SAW:
//    {
//    }
//        break;
//    case CHANNEL_AX8:
//    {
//    }
//        break;
//    case CHANNEL_TEMPERATURE:
//    {
//        //TODO 温湿度智能传感器
//        channel.stachpara.staTempParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
//        channel.stachpara.staTempParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
//        channel.stachpara.staTempParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
//        channel.unID = 0;
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_HUMIDITY:
//    {
//        //TODO 温湿度智能传感器
//        channel.stachpara.staHumParam.fUpperThreshold = items.value("fUpperThreshold").toFloat();
//        channel.stachpara.staHumParam.fLowerThreshold = items.value("fLowerThreshold").toFloat();
//        channel.stachpara.staHumParam.fChangedThreshold = items.value("fChangedThreshold").toFloat();
//        channel.unID = 1;
//        bChannelParam = true;
//    }
//        break;
//    case CHANNEL_FLOOD:
//    {
//        channel.stachpara.stFloodChannelPara.cFlood = items.value("cFlood").toInt();
//        channel.unID = 0;

//    }
//        break;
//    default:
//        break;
//    }

//    ConfigService::instance().getADUTypeFromID(strADUID, eADUType);
//    if (ConfigService::instance().getISADUType(eADUType) && bChannelParam)
//    {
//        MonitorService::instance().setChannelInfo(strADUID, eADUType, channel.unID, channel.stachpara);
//        ConfigService::instance().saveChannelName(strADUID, channel, channel.strName);
//    }
//    else
//    {
//        //待改造
//        ConfigService::instance().saveChannelInfo(strADUID, channel);
//        if (channel.etype == CHANNEL_AE)
//        {
//            channel.stachpara.staAEPara.cGain = items.value("cGain").toInt();
//            MonitorService::instance().setChannelInfo(strADUID, eADUType, channel.unID, channel.stachpara);
//        }
//        if (channel.etype == CHANNEL_TEV)
//        {
//            ConfigService::instance().saveChannelPara(channel.stachpara, strADUID, 1);
//        }
//    }
//}
